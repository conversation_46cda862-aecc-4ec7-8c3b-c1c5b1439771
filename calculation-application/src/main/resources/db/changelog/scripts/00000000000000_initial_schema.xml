<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="PeterWood (generated)" id="1692754181955-1">
        <createTable tableName="fee">
            <column autoIncrement="true" name="fee_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="fee_pkey"/>
            </column>
            <column name="fee_name" type="TEXT"/>
            <column name="payable_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="FLOAT8"/>
            <column defaultValueBoolean="false" name="included" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="collapsible" type="BOOLEAN"/>
            <column name="operation" type="TEXT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-2">
        <createTable tableName="cart_item_fees">
            <column name="cart_item_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="fee_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-3">
        <addUniqueConstraint columnNames="fee_id" constraintName="uk_1acdmj7v388kuuxpreddij09i" tableName="cart_item_fees"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-4">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="cart_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-5">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="cart_item_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-6">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="fee_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-7">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="itemcategory_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-8">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="payment_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-9">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="subpayment_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-10">
        <createTable tableName="cart">
            <column name="cart_id" type="UUID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="cart_pkey"/>
            </column>
            <column name="user_id" type="TEXT"/>
            <column name="created_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="status" type="TEXT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-11">
        <createTable tableName="cart_item">
            <column autoIncrement="true" name="cart_item_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="cart_item_pkey"/>
            </column>
            <column name="category_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="price" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="name" type="TEXT"/>
            <column name="item_identifier" type="TEXT"/>
            <column name="item_id" type="UUID"/>
            <column name="cart_id" type="UUID">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-12">
        <createTable tableName="itemcategory">
            <column autoIncrement="true" name="category_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="itemcategory_pkey"/>
            </column>
            <column name="category_name" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-13">
        <addForeignKeyConstraint baseColumnNames="cart_id" baseTableName="cart_item" constraintName="cart_id_fkey" deferrable="false" initiallyDeferred="false" onDelete="CASCADE" onUpdate="CASCADE" referencedColumnNames="cart_id" referencedTableName="cart" validate="true"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-14">
        <addForeignKeyConstraint baseColumnNames="category_id" baseTableName="cart_item" constraintName="cart_item_category_id_fkey" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="category_id" referencedTableName="itemcategory" validate="true"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-15">
        <addForeignKeyConstraint baseColumnNames="cart_item_id" baseTableName="cart_item_fees" constraintName="fk7c50v2lj7r3a3xyd4wgpb19n9" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="cart_item_id" referencedTableName="cart_item" validate="true"/>
    </changeSet>
    <changeSet author="PeterWood (generated)" id="1692754181955-16">
        <addForeignKeyConstraint baseColumnNames="fee_id" baseTableName="cart_item_fees" constraintName="fkabkami6lqitkvh449h3cfn881" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="fee_id" referencedTableName="fee" validate="true"/>
    </changeSet>
</databaseChangeLog>
