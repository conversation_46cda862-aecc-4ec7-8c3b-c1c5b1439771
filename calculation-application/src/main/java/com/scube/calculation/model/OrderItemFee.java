package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.calculation.enums.FeeType;
import com.scube.calculation.utils.FeeUtils;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.*;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "order_item_fee")
@NoArgsConstructor
@AllArgsConstructor
@Audited
public class OrderItemFee extends AuditableEntity {
    @ManyToOne
    @JoinColumn(name = OrderItem.ORDER_ITEM_ID, nullable = false)
    private OrderItem item;

    @ManyToOne
    @JoinColumn(name = Fee.FEE_ID, nullable = false)
    private Fee fee;

    @Getter(AccessLevel.NONE)
    private BigDecimal price;

    public OrderItemFee(OrderItem item, Fee fee) {
        this.item = item;
        this.fee = fee;
    }

    private BigDecimal getItemTotal() {
        return item.getOrderItemFees().stream()
                .filter(cartItemFee -> !cartItemFee.fee.getOperation().equals(FeeType.PERCENTAGE))
                .map(cartItemFee -> cartItemFee.price != null ? cartItemFee.price : cartItemFee.fee.getAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal calculatePrice() {
        return FeeUtils.calculatePrice(this.price, this.fee, this::getItemTotal);
    }
}