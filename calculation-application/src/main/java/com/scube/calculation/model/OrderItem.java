package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.calculation.dto.AddItemRequest;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = "order_item")
@NoArgsConstructor
@Audited
public class OrderItem extends AuditableEntity implements Item {
    public static final String ORDER_ITEM_ID = "order_item_id";

    @Size(max = 255)
    private String itemTypeId;

    private UUID uniqueItemId;

    @ManyToOne
    @JoinColumn(name = Order.ORDER_ID)
    private Order order;

    private BigDecimal price;

    @Size(max = 255)
    private String name;

    @Size(max = 255)
    private String description;

    @OneToMany(mappedBy = "item", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<OrderItemFee> orderItemFees = new ArrayList<>();

    public void setFees(List<Fee> fees) {
        orderItemFees.clear();
        fees.forEach(fee -> {
            var cartItemFee = new OrderItemFee(this, fee);
            orderItemFees.add(cartItemFee);
        });
    }

    public OrderItem(CartItem cartItem) {
        if (cartItem != null) {
            this.price = cartItem.getPrice();
            this.name = cartItem.getName();
            this.description = cartItem.getDescription();
            this.itemTypeId = cartItem.getItemTypeId();
            this.uniqueItemId = cartItem.getUniqueItemId();
            if (cartItem.getCartItemFees() != null) {
                this.setOrderItemFees(fromCartItem(cartItem));
            }
            if (cartItem.getProperties() != null) {
                super.setProperties(new HashMap<>(cartItem.getProperties()));
            }
        }
    }

    public List<OrderItemFee> fromCartItem(CartItem cartItem) {
        return cartItem.getCartItemFees().stream()
                .map(x -> new OrderItemFee(this, x.getFee(), x.calculatePrice()))
                .toList();
    }

    public OrderItem(AddItemRequest request) {
        if (request.getBasePrice() == null) request.setBasePrice(BigDecimal.ZERO);
        this.price = request.getBasePrice();
        this.name = request.getName();
        this.description = request.getDescription();
        this.itemTypeId = request.getItemType();
        this.uniqueItemId = request.getItemId();
        if (request.getProperties() != null) {
            this.setProperties(new HashMap<>(request.getProperties()));
        }
    }
}