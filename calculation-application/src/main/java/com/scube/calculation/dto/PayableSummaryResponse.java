package com.scube.calculation.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PayableSummaryResponse {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PayableSummaryItem {
        private int payableId;
        private BigDecimal amount;
    }

    private List<PayableSummaryItem> items;
    private BigDecimal total;
}
