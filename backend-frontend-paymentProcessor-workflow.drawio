<mxfile host="app.diagrams.net" modified="2023-06-01T20:16:02.600Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="-edWTJco5BzY7BMjuSp4" version="21.3.4" type="github">
  <diagram name="Page-1" id="KJpiyRFs8mZEmwJa7Rl4">
    <mxGraphModel dx="1434" dy="760" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="aFP5BtcGxjYzzi7cH9ex-2" target="aFP5BtcGxjYzzi7cH9ex-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;" parent="1" source="aFP5BtcGxjYzzi7cH9ex-2" target="aFP5BtcGxjYzzi7cH9ex-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-2" value="licensing frontend" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="210" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-3" value="Backend" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="530" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="aFP5BtcGxjYzzi7cH9ex-4" target="aFP5BtcGxjYzzi7cH9ex-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=1;entryDx=0;entryDy=0;" parent="1" source="aFP5BtcGxjYzzi7cH9ex-4" target="aFP5BtcGxjYzzi7cH9ex-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-4" value="Paypall" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="240" y="510" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-5" value="stripe" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="410" y="600" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-7" value="/getPaymentURL (builds returnURL, postBackURL)" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="240" y="190" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-11" value="/postBack (give us confirmation number and other)" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="600" y="290" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-13" value="status: success, error" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="430" y="390" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aFP5BtcGxjYzzi7cH9ex-15" value="some other processor" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="620" y="580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="mKjpD3MMt9OE953bOn5Q-1" value="frontend ask backend which payment processor&amp;nbsp;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="200" y="148" width="280" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
