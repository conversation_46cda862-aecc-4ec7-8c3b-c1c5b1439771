package com.scube.licensing.features.association.query;

import com.scube.licensing.features.association.find_all_child_associations_by_parent.FindAllChildAssociationsByParentHandler;
import com.scube.licensing.features.association.find_all_child_associations_by_parent.FindAllChildAssociationsByParentQuery;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertIterableEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ActiveProfiles("test")
class FindAllChildAssociationByParentTest {
    @Mock
    private AssociationRepository associationRepository;

    private FindAllChildAssociationsByParentHandler handler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        handler = new FindAllChildAssociationsByParentHandler(associationRepository);
    }

    @Test
    void handle_ValidParentWithAssociations_ReturnsChildAssociations() {
        // Arrange
        var participant = (Participant) new Participant().setId(1L);
        var query = new FindAllChildAssociationsByParentQuery(participant);
        Set<Association> expectedAssociations = Set.of(new Association());

        when(associationRepository.findAllByParentIdAndParentAssociationType(query.parent().getId(), query.parent().getAssociationType()))
                .thenReturn(expectedAssociations);

        // Act
        List<Association> result = handler.handle(query);

        // Assert
        assertIterableEquals(expectedAssociations, result);
        verify(associationRepository, times(1))
                .findAllByParentIdAndParentAssociationType(query.parent().getId(), query.parent().getAssociationType());
    }

    @Test
    void handle_ValidParentWithNoAssociations_ReturnsEmptyList() {
        // Arrange
        var participant = (Participant) new Participant().setId(1L);
        var query = new FindAllChildAssociationsByParentQuery(participant);
        Set<Association> expectedAssociations = Collections.emptySet();

        when(associationRepository.findAllByParentIdAndParentAssociationType(query.parent().getId(), query.parent().getAssociationType()))
                .thenReturn(expectedAssociations);

        // Act
        List<Association> result = handler.handle(query);

        // Assert
        assertIterableEquals(expectedAssociations, result);
        verify(associationRepository, times(1))
                .findAllByParentIdAndParentAssociationType(query.parent().getId(), query.parent().getAssociationType());
    }

    @Test
    void handle_NullParent_ThrowsIllegalArgumentException() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> new FindAllChildAssociationsByParentQuery(null));
        verifyNoInteractions(associationRepository);
    }

    @Test
    void handle_ParentWithNullId_ThrowsIllegalArgumentException() {
        // Arrange
        var parent = (Participant) new Participant().setId(null);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> new FindAllChildAssociationsByParentQuery(parent));
        verifyNoInteractions(associationRepository);
    }
}