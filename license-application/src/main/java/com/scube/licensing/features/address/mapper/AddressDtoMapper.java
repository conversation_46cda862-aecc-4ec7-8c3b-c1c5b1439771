package com.scube.licensing.features.address.mapper;

import com.scube.licensing.features.profile.dto.AddressDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring", uses = {EventMapper.class})
public abstract class AddressDtoMapper {
    @Mapping(target = "entityId", source = "uuid")
    @Mapping(target = "customFields", source = "properties")
    public abstract AddressDto toDto(Address address);

    public abstract List<AddressDto> toDto(List<Address> addresses);
}