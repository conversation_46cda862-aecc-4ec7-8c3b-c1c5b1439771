package com.scube.licensing.features.profile.get_profile;

import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import lombok.RequiredArgsConstructor;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetPro<PERSON>leHandler implements IRequestHandlerAxon<GetProfileQuery, GetProfileResponse> {
    private final ProfileService profileService;

    @Override
    @QueryHandler
    public GetProfileResponse handle(GetProfileQuery query) {
        Associable profile = profileService.getProfileOrElseThrow(query.profileType(), query.entityId());

        return new GetProfileResponse(profile);
    }
}