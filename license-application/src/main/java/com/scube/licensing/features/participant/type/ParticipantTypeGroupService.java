package com.scube.licensing.features.participant.type;

import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantTypeGroup;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantTypeGroupRepository;
import org.springframework.stereotype.Service;

@Service
public class ParticipantTypeGroupService {
    private final ParticipantTypeGroupRepository participantTypeGroupRepository;

    public ParticipantTypeGroupService(ParticipantTypeGroupRepository participantTypeGroupRepository) {
        this.participantTypeGroupRepository = participantTypeGroupRepository;
    }

    public ParticipantTypeGroup getParticipantTypeGroup(String participantTypeName, String participantGroupName) {
        return participantTypeGroupRepository.findByParticipantTypeNameAndParticipantGroupName(participantTypeName, participantGroupName)
                .orElseThrow(() -> new ParticipantTypeGroupNotFoundException("ParticipantTypeGroup not found with participantTypeName: " + participantTypeName + " and participantGroupName: " + participantGroupName));
    }

    public ParticipantTypeGroup getParticipantTypeGroup(Long id) {
        return participantTypeGroupRepository.findById(id)
                .orElseThrow(() -> new ParticipantTypeGroupNotFoundException("ParticipantTypeGroup not found with id: " + id));
    }
}