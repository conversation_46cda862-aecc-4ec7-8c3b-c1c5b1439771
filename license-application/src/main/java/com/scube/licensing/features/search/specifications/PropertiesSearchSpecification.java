package com.scube.licensing.features.search.specifications;

import com.scube.audit.auditable.entity.AuditableBase;
import com.scube.audit.auditable.entity.AuditableBaseWithProperties;
import com.scube.audit.auditable.properties.FindByOption;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
public class PropertiesSearchSpecification<T> implements Specification<T> {
    private final List<Field> properties;
    private final Map<String, Object> params;
    private final FindByOption option;

    @Override
    public Predicate toPredicate(@NonNull Root<T> root, CriteriaQuery<?> query, @NonNull CriteriaBuilder cb) {
        if (query == null)
            throw new IllegalArgumentException("CriteriaQuery cannot be null");
        // order by CREATED_DATE so that in the case of findFirst the latest entity is returned
        query.orderBy(cb.desc(root.get(AuditableBase.Fields.createdDate)));

        List<Predicate> predicates = new ArrayList<>();
        for (var field : properties) {
            String key = getKey(field);
            Object value = getValue(field);

            if (value != null) {
                var strValue = value.toString();
                var jsonExtractExpression = cb.function(
                        "jsonb_extract_path_text",
                        String.class,
                        root.get(AuditableBaseWithProperties.A_PROPERTIES),
                        cb.literal(key)
                );

                Expression<?> expression = null;
                if (field.isCoalesce()) {
                    expression = cb.coalesce()
                            .value(jsonExtractExpression)
                            .value(getDefaultValue(field, value));
                } else {
                    expression = jsonExtractExpression;
                }
                // use the fn_ilike function to perform a case-insensitive comparison
                Expression<Boolean> ilikeExpression = cb.function(
                        "fn_ilike",
                        Boolean.class,
                        expression,
                        cb.literal(strValue)
                );

                Predicate predicate = cb.isTrue(ilikeExpression);
                predicates.add(predicate);
            }
        }

        if (option == FindByOption.AND)
            return cb.and(predicates.toArray(new Predicate[0]));

        return cb.or(predicates.toArray(new Predicate[0]));
    }

    private Object getDefaultValue(Field field, Object value) {
        if (field.isCoalesce() && field.getCoalesceDefaultValue() != null) {
            return field.getCoalesceDefaultValue();
        }
        return switch (value) {
            case String s -> "";
            case Integer i -> 0;
            case Long l -> 0L;
            case Double d -> 0.0;
            case Float f -> 0.0f;
            case Boolean b -> false;
            default -> null;
        };
    }

    public String getKey(Field field) {
        return field.getFieldName();
    }

    public Object getValue(Field field) {
        return params.get(field.getFieldName());
    }
}