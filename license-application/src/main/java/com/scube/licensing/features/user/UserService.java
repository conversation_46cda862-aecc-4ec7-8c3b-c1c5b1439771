package com.scube.licensing.features.user;

import com.scube.licensing.features.user.get_user_by_username.GetUserByUsernameQuery;
import com.scube.licensing.features.user.get_user_by_username.GetUserByUsernameQueryResponse;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {
    protected final AmqpGateway amqpGateway;

    @Cacheable("getUserNameByUsername")
    public String getUserNameByUsername(String username) {
        if (ObjectUtils.isEmpty(username)) {
            return "anonymous";
        }

        return amqpGateway.queryResult(new GetUserByUsernameQuery(username))
                .orElse(new GetUserByUsernameQueryResponse(username)).name();
    }

    // cache evict after 30 minutes via scheduler
    @Scheduled(fixedRate = 1000 * 60 * 30)
    @CacheEvict(value = "getUserNameByUsername", allEntries = true)
    public void evictAllCacheValues() {
        log.debug("Evicting all getUserNameByUsername cache");
    }
}