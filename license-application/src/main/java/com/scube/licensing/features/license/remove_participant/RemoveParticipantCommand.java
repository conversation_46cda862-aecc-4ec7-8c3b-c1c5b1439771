package com.scube.licensing.features.license.remove_participant;

import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RemoveParticipantCommand implements IRequestVoidAxon {
    private UUID entityId;
    private UUID participantId;
}