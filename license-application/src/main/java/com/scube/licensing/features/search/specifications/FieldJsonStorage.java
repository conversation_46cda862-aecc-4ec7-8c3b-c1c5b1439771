package com.scube.licensing.features.search.specifications;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FieldJsonStorage {

    @JsonAnySetter
    @JsonAnyGetter
    private Map<String, List<Field>> fieldMap;

    public List<Field> getFields(String... categories) {
        return fieldMap.entrySet().stream()
                .filter(entry -> entry.getKey().equalsIgnoreCase("all") || Stream.of(categories).anyMatch(entry.getKey()::equalsIgnoreCase))
                .flatMap(entry -> entry.getValue().stream())
                .toList();
    }

    public List<Field> getFields() {
        return fieldMap.values().stream()
                .flatMap(Collection::stream)
                .filter(field -> field.getFieldName() != null)
                .toList();
    }
}