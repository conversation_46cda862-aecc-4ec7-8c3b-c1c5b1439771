package com.scube.licensing.features.settings.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class AppPropertyBadRequestException extends RuntimeException {
    public AppPropertyBadRequestException() {
    }

    public AppPropertyBadRequestException(String message) {
        super(message);
    }

    public AppPropertyBadRequestException(String message, Throwable cause) {
        super(message, cause);
    }

    public AppPropertyBadRequestException(Throwable cause) {
        super(cause);
    }
}
