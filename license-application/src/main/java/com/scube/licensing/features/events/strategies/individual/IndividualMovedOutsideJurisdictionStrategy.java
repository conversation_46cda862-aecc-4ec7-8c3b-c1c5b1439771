package com.scube.licensing.features.events.strategies.individual;

import com.scube.licensing.features.events.dto.EventRequestDto;
import com.scube.licensing.features.events.service.util.EventLicenseFormService;
import com.scube.licensing.features.events.strategies.IEventStrategyService;
import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

@Service("individualMovedOutsideJurisdiction")
@Slf4j
@AllArgsConstructor
public class IndividualMovedOutsideJurisdictionStrategy implements IEventStrategyService {
    private final LicenseService licenseService;
    private final EventLicenseFormService licenseFormService;
    private final ParticipantService participantService;

    @Override
    public Object execute(Associable profile, EventRequestDto dto) {
        Participant individual = (Participant) profile;

        if (individual.getParticipantStatus().getName().equals("Deceased")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid status change");
        }

        participantService.setStatus(individual, "Moved Out Of Jurisdiction");

        participantService.save(individual);

        licenseService.changeAllLicenseStatuses(individual, "Canceled","Moved");

        licenseFormService.regenerateLicenseForms(individual);

        return participantService.toDto(individual);
    }
}
