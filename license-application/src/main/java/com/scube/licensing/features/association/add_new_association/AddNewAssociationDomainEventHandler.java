package com.scube.licensing.features.association.add_new_association;

import com.scube.licensing.features.association.save_association.SaveAssociationCommand;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class AddNewAssociationDomainEventHandler {
    private final AxonGateway axonGateway;
    private final ProfileService profileService;

    @Async // DR: Won't work without this
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleAddNewAssociation(AddNewAssociationDomainEvent event) {
        log.info("Adding new association between parent: {} and child: {}", event.getParent(), event.getChild());
        if (event.getParent() == null) {
            event.setParent(profileService.getProfileOrElseThrow(event.getParentEntityType(), event.getParentEntityId()));
        }

        if (event.getChild() == null) {
            event.setChild(profileService.getProfileOrElseThrow(event.getChildEntityType(), event.getChildEntityId()));
        }

        axonGateway.sendAndWait(new SaveAssociationCommand(event.getParent(), event.getChild(), event.getProperties()));
        log.info("Done adding new association between parent: {} and child: {}", event.getParent(), event.getChild());
    }
}