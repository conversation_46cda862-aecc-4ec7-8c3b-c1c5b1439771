package com.scube.licensing.features.events.service;

import com.scube.licensing.features.events.dto.EventTypeDto;
import com.scube.licensing.features.events.mapper.EventTypeDtoMapper;
import com.scube.licensing.infrastructure.db.entity.event.EventType;
import com.scube.licensing.infrastructure.db.repository.event.EventTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional
public class EventTypeService {
    private final EventTypeRepository eventTypeRepository;
    private final EventTypeDtoMapper eventTypeDtoMapper;

    public boolean exists(String eventTypeCode) {
        return eventTypeRepository.existsByCode(eventTypeCode);
    }

    public EventTypeDto getEventType(String eventTypeCode) {
        var event = eventTypeRepository.findByCode(eventTypeCode)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Event type " + eventTypeCode + " not found"));
        return eventTypeDtoMapper.toDto(event);
    }

    public EventTypeDto getEventType(Long eventTypeId) {
        EventType event = eventTypeRepository.findById(eventTypeId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Event type " + eventTypeId + " not found"));
        return eventTypeDtoMapper.toDto(event);
    }

    public List<EventTypeDto> getEventTypes() {
        var events = eventTypeRepository.findAll();
        return eventTypeDtoMapper.toDto(events);
    }

    public List<EventTypeDto> getByProfileType(String profileType) {
        var events = eventTypeRepository.findByProfileTypeName(profileType);
        return eventTypeDtoMapper.toDto(events);
    }

    public EventTypeDto createEventType(EventTypeDto dto) {
        var event = eventTypeDtoMapper.toEntity(dto);
        event = eventTypeRepository.save(event);
        return eventTypeDtoMapper.toDto(event);
    }

    public void deleteEventType(Long id) {
        eventTypeRepository.deleteById(id);
    }

    public void deleteEventType(String code) {
        var event = eventTypeRepository.findByCode(code)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Event type " + code + " not found"));
        eventTypeRepository.delete(event);
    }
}