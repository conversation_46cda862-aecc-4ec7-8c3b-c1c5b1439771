package com.scube.licensing.features.association;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.features.entity.dtos.EntityTypeEnum;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import com.scube.licensing.infrastructure.db.entity.business.Business;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import com.scube.licensing.infrastructure.db.entity.entity_group.EntityGroup;
import com.scube.licensing.infrastructure.db.entity.entity_note.EntityNote;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.licensing.infrastructure.db.repository.address.AddressRepository;
import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import com.scube.licensing.infrastructure.db.repository.business.BusinessRepository;
import com.scube.licensing.infrastructure.db.repository.document.DocumentRepository;
import com.scube.licensing.infrastructure.db.repository.entity_fee.EntityFeeRepository;
import com.scube.licensing.infrastructure.db.repository.entity_group.EntityGroupRepository;
import com.scube.licensing.infrastructure.db.repository.entity_note.EntityNoteRepository;
import com.scube.licensing.infrastructure.db.repository.license.LicenseRepository;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AssociationService {
    private final AssociationRepository associationRepository;
    private final LicenseRepository licenseRepository;
    private final ParticipantRepository participantRepository;
    private final DocumentRepository documentRepository;
    private final AddressRepository addressRepository;
    private final EntityGroupRepository entityGroupRepository;
    private final EntityFeeRepository entityFeeRepository;
    private final BusinessRepository businessRepository;
    private final EntityNoteRepository entityNoteRepository;

    public void deleteAll(List<Association> associationToDelete) {
        associationRepository.deleteAll(associationToDelete);
    }

    public void saveAll(List<Association> associationToSave) {
        associationRepository.saveAll(associationToSave);
    }

    public void save(Association association) {
        associationRepository.save(association);
    }

    public Set<Association> getChildAssociations(Associable entity) {
        var start = System.currentTimeMillis();
        log.debug("Loading associations for {}:{}", entity.getAssociationType(), entity.getId());
        Set<Association> childAssociations = associationRepository.findAllByParentIdAndParentAssociationType(entity.getId(), entity.getAssociationType());

        populateAssociables(entity, childAssociations, true);

        var end = System.currentTimeMillis() - start;
        log.debug("Done loading associations for {}:{} in {}ms", entity.getAssociationType(), entity.getId(), end);
        return childAssociations;
    }

    public Set<Association> getParentAssociations(Associable entity) {
        var start = System.currentTimeMillis();
        log.debug("Loading associations for {}:{}", entity.getAssociationType(), entity.getId());
        Set<Association> parentAssociations = associationRepository.findAllByChildIdAndChildAssociationType(entity.getId(), entity.getAssociationType());

        populateAssociables(entity, parentAssociations, false);

        var end = System.currentTimeMillis() - start;
        log.debug("Done loading associations for {}:{} in {}ms", entity.getAssociationType(), entity.getId(), end);
        return parentAssociations;
    }

    private void populateAssociables(Associable entity, Set<Association> associations, boolean isChild) {
        Map<AssociationType, Set<Long>> idsMap = new EnumMap<>(AssociationType.class);

        // Collect IDs grouped by type
        associations.forEach(association -> {
            if (isChild) {
                addToIdMap(association.getChildId(), association.getChildAssociationType(), idsMap);
            } else {
                addToIdMap(association.getParentId(), association.getParentAssociationType(), idsMap);
            }
        });

        // Remove the IDs that already exist in the entity
        removeEntityFromIdMap(entity, idsMap);

        // Fetch entities grouped by type
        Map<AssociationType, Map<Long, ? extends Associable>> entitiesMap = idsMap.entrySet().stream()
                .collect(HashMap::new, (map, e) -> map.put(e.getKey(), this.findAllByIds(e)), Map::putAll);

        // Populate entities into the association relationships
        associations.forEach(assoc -> {
            if (isChild) {
                Associable child = getAssociableFromMap(assoc.getChildId(), assoc.getChildAssociationType(), entitiesMap);
                assoc.setChild(child);
                assoc.setParent(entity);
            } else {
                Associable parent = getAssociableFromMap(assoc.getParentId(), assoc.getParentAssociationType(), entitiesMap);
                assoc.setChild(entity);
                assoc.setParent(parent);
            }
        });
    }

    private HashMap<Long, ? extends Associable> findAllByIds(Map.Entry<AssociationType, Set<Long>> entry) {
        var repo = getRepository(entry.getKey());
        return repo.findAllById(entry.getValue()).stream()
                .collect(HashMap::new, (map, e) -> map.put(e.getId(), e), Map::putAll);
    }

    private void addToIdMap(Long associableId, AssociationType associationType, Map<AssociationType, Set<Long>> idsMap) {
        idsMap.computeIfAbsent(associationType, k -> new HashSet<>()).add(associableId);
    }

    private void removeEntityFromIdMap(Associable entity, Map<AssociationType, Set<Long>> idsMap) {
        Set<Long> ids = idsMap.getOrDefault(entity.getAssociationType(), Collections.emptySet());
        ids.remove(entity.getId());
    }

    private Associable getAssociableFromMap(Long associableId, AssociationType associationType, Map<AssociationType, Map<Long, ? extends Associable>> entityMap) {
        Map<Long, ? extends Associable> associableMap = entityMap.get(associationType);
        Optional<? extends Associable> assoc = Optional.ofNullable(associableMap.get(associableId));
        if (assoc.isPresent()) {
            return assoc.get();
        } else {
            var repo = getRepository(associationType);
            return repo.findById(associableId)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "%s with ID %s not found".formatted(associationType, associableId)));
        }
    }

    @NonNull
    public AuditableEntityRepository<? extends Associable, Long> getRepository(AssociationType associationType) {
        return switch (associationType) {
            case CUSTOM_ENTITY -> throw new IllegalArgumentException("Custom entity is not supported");
            case LICENSE -> licenseRepository;
            case PARTICIPANT -> participantRepository;
            case DOCUMENT -> documentRepository;
            case ADDRESS -> addressRepository;
            case ENTITY_FEE -> entityFeeRepository;
            case BUSINESS -> businessRepository;
            case ENTITY_NOTE -> entityNoteRepository;
            case ENTITY_GROUP -> entityGroupRepository;
        };
    }

    @NonNull
    public AuditableEntityRepository<? extends Associable, Long> getRepository(EntityTypeEnum entityTypeEnum) {
        return switch (entityTypeEnum) {
            case CUSTOM_ENTITY -> throw new IllegalArgumentException("Custom entity is not supported");
            case LICENSE -> licenseRepository;
            case INDIVIDUAL, DOG -> participantRepository;
            case DOCUMENT -> documentRepository;
            case ADDRESS -> addressRepository;
            case ENTITY_FEE -> entityFeeRepository;
            case BUSINESS -> businessRepository;
            case ENTITY_NOTE -> entityNoteRepository;
            case ENTITY_GROUP -> entityGroupRepository;
        };
    }

    public Associable saveAssociable(Associable entity) {
        return switch (entity.getAssociationType()) {
            case CUSTOM_ENTITY -> throw new IllegalArgumentException("Custom entity not supported");
            case LICENSE -> licenseRepository.save((License) entity);
            case PARTICIPANT -> participantRepository.save((Participant) entity);
            case DOCUMENT -> documentRepository.save((Document) entity);
            case ADDRESS -> addressRepository.save((Address) entity);
            case ENTITY_FEE -> entityFeeRepository.save((EntityFee) entity);
            case BUSINESS -> businessRepository.save((Business) entity);
            case ENTITY_NOTE -> entityNoteRepository.save((EntityNote) entity);
            case ENTITY_GROUP -> entityGroupRepository.save((EntityGroup) entity);
        };
    }
}