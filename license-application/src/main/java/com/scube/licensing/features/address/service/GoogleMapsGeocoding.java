package com.scube.licensing.features.address.service;

import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.GeocodingResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Optional;

@Component
@Slf4j
@ConditionalOnProperty(name = "geocoding.type", havingValue = "google")
public class GoogleMapsGeocoding implements IGeocoding {
    @Value("${GOOGLE_MAPS_API_KEY}")
    private String apiKey;

    @Override
    public Optional<GeocodingResponse> getGeocoding(String street, String city, String state, String postalCode) {
        GeoApiContext context = null;
        try {
            context = new GeoApiContext.Builder()
                    .apiKey(apiKey)
                    .build();
            var buildFullAddress = buildStreetAddress(street, city, state, postalCode);
            GeocodingResult[] results = GeocodingApi.geocode(context, buildFullAddress).await();

            if (results.length == 0) return Optional.empty();
            var result = new GeocodingResponse(
                    getComponent(results, "street_number"),
                    getComponent(results, "route"),
                    getComponent(results, "locality"),
                    getComponent(results, "administrative_area_level_3"),
                    getComponent(results, "administrative_area_level_2"),
                    getComponent(results, "administrative_area_level_1"),
                    getComponent(results, "country"),
                    getComponent(results, "postal_code"),
                    results[0].geometry.location.lat,
                    results[0].geometry.location.lng
            );
            return Optional.of(result);
        } catch (Exception e) {
            log.error("Error getting geocoding for address: " + street);
            log.error(Arrays.toString(e.getStackTrace()));
        } finally {
            assert context != null;
            context.shutdown();
        }
        return Optional.empty();
    }

    private static String getComponent(GeocodingResult[] results, String name) {
        //prevent null pointer exception
        if (results[0].addressComponents == null || results[0].addressComponents.length == 0) return null;

        var component = Arrays.stream(results[0].addressComponents)
                .filter(x -> x.types != null && Arrays.stream(x.types).anyMatch(i -> i.name().equalsIgnoreCase(name))).findFirst();
        if (component.isEmpty()) return null;
        return component.get().longName;
    }
}