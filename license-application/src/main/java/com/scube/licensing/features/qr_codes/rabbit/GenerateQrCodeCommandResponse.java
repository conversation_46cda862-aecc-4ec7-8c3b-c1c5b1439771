package com.scube.licensing.features.qr_codes.rabbit;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

@Data
@NoArgsConstructor
public class GenerateQrCodeCommandResponse {
    private byte[] bytes;
    private GenerateQrCodeCommand request;

    @SneakyThrows
    public GenerateQrCodeCommandResponse(MultipartFile file, GenerateQrCodeCommand request) {
        this.bytes = IOUtils.toByteArray(file.getInputStream());
        this.request = request;
    }
}