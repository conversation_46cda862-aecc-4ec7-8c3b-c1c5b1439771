package com.scube.licensing.features.license.partial_save.create_final_license;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
public class CreateFinalLicenseResponse {

    private UUID entityId;
    private String entityType;

    public CreateFinalLicenseResponse(UUID entityId, String entityType) {
        this.entityId = entityId;
        this.entityType = entityType.toLowerCase();
    }
}