package com.scube.licensing.features.events.strategies.dog;

import com.scube.licensing.features.events.dto.EventRequestDto;
import com.scube.licensing.features.events.service.util.EventLicenseFormService;
import com.scube.licensing.features.events.strategies.IEventStrategyService;
import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.features.license.partial_save.create_final_license.CreateFinalLicenseCommand;
import com.scube.licensing.features.license.partial_save.create_initial_license.CreateInitialLicenseCommand;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.participant.dog.DogHandler;
import com.scube.licensing.features.participant.dog.DogService;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service("dogTransferOfOwnership")
@Slf4j
@AllArgsConstructor
public class DogTransferOfOwnershipStrategy implements IEventStrategyService {
    private final ParticipantService participantService;
    private final AxonGateway axonGateway;
    private final LicenseService licenseService;
    private final EventLicenseFormService licenseFormService;
    private final DogService dogService;

    @Override
    public Object execute(Associable profile, EventRequestDto dto) {
        var params = dto.fields();
        var dog = (Participant) profile;

        var autoApproval = Boolean.parseBoolean(params.getOrDefault("autoApproval", "false"));

        //Process all updates as a transaction before generating license forms
        License license = transactionalUpdate(params, dog, autoApproval);

        // regenerate license forms
        licenseFormService.regenerateLicenseForms(dog);

        return licenseService.toDto(license);
    }

    @Transactional
    private License transactionalUpdate(Map<String, String> params, Participant transferee, boolean autoApproval) {
        if (transferee.getParticipantStatus().getName().equals("Deceased")
            || transferee.getParticipantStatus().getName().equals("Transferred")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid status change");
        }

        //Creates new license and associates it to the new owner
        //todo determine licenseType somehow
        UUID licenseId = axonGateway.sendAndWait(new CreateInitialLicenseCommand(UUID.fromString(params.get("newOwnerId")), "dogLicense")).entityId();

        //retrieve the new license
        License license = axonGateway.query(new LicenseService.FindLicenseByEntityIdQuery(licenseId))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, String.format("License with entity id %s not found", licenseId)));

        //Copy the dog data to the new license
        Map<String, String> oldLicenseData = transferee.getProperties().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().toString()));

        oldLicenseData.putAll(params);

        //Creates a new dog and associates it to the new license, new owner, and new owner address
        dogService.addDogToLicense(licenseId, oldLicenseData, Map.of());

        //Finalize the new license
        axonGateway.sendAndWait(new CreateFinalLicenseCommand(licenseId, null, autoApproval, null, null));

        // update dog status to transferred
        participantService.setStatus(transferee, "Transferred");
        participantService.save(transferee);

        //Cancel old license
        axonGateway.sendAndWait(new DogHandler.UpdateDogsLicenseStatusCommand(transferee, "Canceled", "Transfer"));

        return license;
    }
}
