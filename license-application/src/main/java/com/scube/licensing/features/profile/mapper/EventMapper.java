package com.scube.licensing.features.profile.mapper;

import com.scube.licensing.features.events.Event;
import com.scube.licensing.features.profile.dto.EventDto;
import com.scube.licensing.features.user.UserService;
import com.scube.licensing.infrastructure.db.entity.event.EventType;
import com.scube.licensing.infrastructure.db.repository.event.EventTypeRepository;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.util.List;

@Slf4j
@Mapper(componentModel = "spring")
public abstract class EventMapper {
    @Autowired
    protected EventTypeRepository eventTypeRepository;

    @Autowired
    protected UserService userService;

    @Mapping(target = "createdBy", expression = "java(getCreatedBy(event))")
    @Mapping(target = "code", source = "event.eventTypeCode")
    @BeanMapping(qualifiedByName = "toDtoAfterMapping")
    protected abstract EventDto toDto(Event event);

    @Named("toDtoAfterMapping")
    @AfterMapping
    public void toDtoAfterMapping(@MappingTarget EventDto dto) {
        EventType eventType = null;
        if (!ObjectUtils.isEmpty(dto.getEventTypeId())) {
            eventType = eventTypeRepository.findById(dto.getEventTypeId()).orElseThrow();
        } else if (!ObjectUtils.isEmpty(dto.getCode())) {
            eventType = eventTypeRepository.findByCode(dto.getCode()).orElseThrow();
            dto.setEventTypeId(eventType.getId());
        } else {
            throw new IllegalArgumentException("Event type id or code is required");
        }
        dto.setCode(eventType.getCode());
        dto.setName(eventType.getName());
        dto.setDescription(eventType.getDescription());
    }

    public abstract Event toEntity(EventDto eventDto);

    public abstract List<EventDto> toDto(List<Event> events);

    public abstract List<Event> toEntity(List<EventDto> eventDtos);

    public String getCreatedBy(Event event) {
        return userService.getUserNameByUsername(event.getCreatedBy());
    }
}