package com.scube.licensing.features.license.reports.service;

import com.scube.licensing.features.license.fee.sql_fees.FeeCalculationResponse;
import com.scube.licensing.features.license.fee.sql_fees.LicenseFeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReportServiceHelper {
    private final LicenseFeeService licenseFeeService;

    @Async
    public CompletableFuture<Void> recalculateFees(Map<String, Object> licenseData, boolean isResident) {
        var licenseTypeCode = (String) licenseData.get("license_type_code");

        log.debug("Recalculating fees for license type: {}", licenseTypeCode);

        FeeCalculationResponse response = licenseFeeService.calculateAndGetFees(licenseData, isResident);

        licenseData.put("city_fee", response.getCityFee().toString());
        licenseData.put("state_fee", response.getStateFee().toString());
        licenseData.put("discount_amount", response.getDiscounts().toString());
        licenseData.put("penalty_fee", response.getPenalty().toString());
        licenseData.put("subtotal_fees", response.getSubtotalFees().toString());
        licenseData.put("subtotal_fees_with_discounts", response.getSubtotalFeesWithDiscounts().toString());
        licenseData.put("total_fees", response.getTotal().toString());

        return CompletableFuture.completedFuture(null);
    }
}
