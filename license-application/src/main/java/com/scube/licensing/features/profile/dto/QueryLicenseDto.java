package com.scube.licensing.features.profile.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
public class QueryLicenseDto implements IEntityDto {
    private UUID entityId;
    private String licenseNumber;
    private Instant issuedDate;
    private Instant applicationDate;
    private Instant validFromDate;
    private Instant validToDate;
    private LicenseTypeDto licenseType;
    private String description;
    private String status;
    private String activityType;
    private String modifier;
    private boolean approved;
    private Instant approvedDate;
    private String deniedComment;
    private String createdBy;
    private String lastModifiedBy;
    private Instant createdDate;
    private Instant lastModifiedDate;
    private List<QueryParticipantDto> dogs;
    private int householdDogLicenseCount;
    private UUID receiptId;

    @JsonIgnore
    private Map<String, Object> customFields;
    @JsonIgnore
    private String tableName;

    @JsonAnyGetter
    public Map<String, Object> getCustomFields() {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        return customFields.entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId"))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
    }

    public boolean isDraftLicense() {
        return status != null && status.equalsIgnoreCase("draft");
    }

    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }
}