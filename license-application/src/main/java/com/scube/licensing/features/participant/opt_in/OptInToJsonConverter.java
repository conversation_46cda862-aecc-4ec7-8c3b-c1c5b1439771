package com.scube.licensing.features.participant.opt_in;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.licensing.infrastructure.db.entity.participant.opt_in.OptIn;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Converter
@AllArgsConstructor
public class OptInToJsonConverter implements AttributeConverter<List<OptIn>, String> {
    private final ObjectMapper objectMapper;

    @Override
    public String convertToDatabaseColumn(List<OptIn> optIns) {
        if (optIns == null)
            return null;

        try {
            return objectMapper.writeValueAsString(optIns);
        } catch (Exception e) {
            log.error("OptInToJsonConverter.convertToDatabaseColumn() error", e);
            return null;
        }
    }

    @Override
    public List<OptIn> convertToEntityAttribute(String optInsJson) {
        if (optInsJson == null || optInsJson.isEmpty())
            return new ArrayList<>();

        try {
            return objectMapper.readValue(optInsJson, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("OptInToJsonConverter.convertToEntityAttribute() error", e);
            return new ArrayList<>();
        }
    }
}
