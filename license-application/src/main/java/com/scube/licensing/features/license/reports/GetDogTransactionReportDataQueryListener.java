package com.scube.licensing.features.license.reports;

import com.scube.licensing.features.license.reports.projections.LicenseProjection;
import com.scube.licensing.infrastructure.db.repository.license.LicenseRepository;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;


@AllArgsConstructor
@Component
@Slf4j
public class GetDogTransactionReportDataQueryListener extends FanoutListenerRpc<GetDogTransactionReportDataQueryListener.GetDogTransactionReportDataQuery, GetDogTransactionReportDataQueryListener.GetDogTransactionReportDataResponse> {
    private final LicenseRepository licenseRepository;

    public RabbitResult<GetDogTransactionReportDataResponse> consume(GetDogTransactionReportDataQueryListener.GetDogTransactionReportDataQuery event) {
        return RabbitResult.of(() -> new GetDogTransactionReportDataResponse(licenseRepository.getDogTransactionReport(event.getStartDate(), event.getEndDate())));
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GetDogTransactionReportDataQuery implements IRabbitFanoutSubscriberRpc<GetDogTransactionReportDataResponse> {
        private LocalDate startDate;
        private LocalDate endDate;
    }

    public record GetDogTransactionReportDataResponse(List<LicenseProjection> licenses) {
    }
}
