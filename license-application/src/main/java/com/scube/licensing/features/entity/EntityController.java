package com.scube.licensing.features.entity;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.entity.dtos.EntityTypeEnum;
import com.scube.licensing.features.entity.service.EntityService;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.infrastructure.validation.NullOrUndefinedToNull;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/entity")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
@Validated
public class EntityController {
    private final EntityService entityService;

    @PostMapping(value = "{entityType}/create")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Entity.CREATE_ENTITY)
    public Object createEntity(@PathVariable EntityTypeEnum entityType, @RequestBody @NullOrUndefinedToNull Map<String, Object> fields) {
        return entityService.createEntity(entityType, fields, Map.of());
    }

    @PatchMapping(value = "{entityType}/{entityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Entity.UPDATE_ENTITY)
    public Object updateEntity(@PathVariable EntityTypeEnum entityType, @PathVariable UUID entityId, @RequestBody @NullOrUndefinedToNull Map<String, Object> fields) {
        return entityService.updateEntity(entityType, entityId, fields, Map.of());
    }

    @DeleteMapping(value = "{entityType}/{entityId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Entity.DELETE_ENTITY)
    public void deleteEntity(@PathVariable EntityTypeEnum entityType, @PathVariable UUID entityId) {
        entityService.deleteEntity(entityType, entityId);
    }

    @PostMapping(value = "{entityType}/{entityId}/add-association")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Entity.ADD_ASSOCIATION)
    public Object addAssociation(@PathVariable EntityTypeEnum entityType, @PathVariable UUID entityId, @RequestBody @NullOrUndefinedToNull Map<String, Object> fields) {
        return entityService.addAssociation(entityType, entityId, fields);
    }

    @PostMapping(value = "{entityType}/{entityId}/remove-association")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Entity.REMOVE_ASSOCIATION)
    public Object removeAssociation(@PathVariable EntityTypeEnum entityType, @PathVariable UUID entityId, @RequestBody @NullOrUndefinedToNull Map<String, Object> fields) {
        return entityService.removeAssociation(entityType, entityId, fields);
    }
}