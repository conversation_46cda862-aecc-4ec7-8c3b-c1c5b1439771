package com.scube.licensing.features.profile;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;
import java.util.UUID;

@RestController
@RequestMapping("profile")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
@Validated
public class ProfileController {
    private final ProfileService profileService;
    private final RejectedFieldsService rejectedFieldsService;

    @GetMapping("/individual/{individualEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Profile.GET_INDIVIDUAL_ENTITY_AND_ALL_ASSOCIATIONS)
    public JsonNode getIndividualEntityAndAllAssociations(@PathVariable UUID individualEntityId) {
        return profileService.getProfileAndAssociationsAsJsonReadOnly(individualEntityId, "individual");
    }

    @GetMapping("/license/{licenseEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Profile.GET_LICENSE_ENTITY_AND_ALL_ASSOCIATIONS)
    public JsonNode getLicenseEntityAndAllAssociations(@PathVariable UUID licenseEntityId) {
        return profileService.getProfileAndAssociationsAsJsonReadOnly(licenseEntityId, "license");
    }

    @GetMapping("/dog/{dogEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Profile.GET_DOG_ENTITY_AND_ALL_ASSOCIATIONS)
    public JsonNode getDogEntityAndAllAssociations(@PathVariable UUID dogEntityId) {
        return profileService.getProfileAndAssociationsAsJsonReadOnly(dogEntityId, "dog");
    }

    @GetMapping({"/address/{addressEntityId}", "/parcel/{addressEntityId}"})
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Profile.GET_ADDRESS_ENTITY_AND_ALL_ASSOCIATIONS)
    public JsonNode getAddressEntityAndAllAssociations(@PathVariable UUID addressEntityId) {
        return profileService.getProfileAndAssociationsAsJsonReadOnly(addressEntityId, "address");
    }

    @GetMapping("/document/{documentEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Profile.GET_DOCUMENT_ENTITY_AND_ALL_ASSOCIATIONS)
    public JsonNode getDocumentEntityAndAllAssociations(@PathVariable UUID documentEntityId) {
        return profileService.getProfileAndAssociationsAsJsonReadOnly(documentEntityId, "document");
    }

    @GetMapping("/business/{businessEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Profile.GET_BUSINESS_ENTITY_AND_ALL_ASSOCIATIONS)
    public JsonNode getBusinessEntityAndAllAssociations(@PathVariable UUID businessEntityId) {
        return profileService.getProfileAndAssociationsAsJsonReadOnly(businessEntityId, "business");
    }

    @PatchMapping("/{entityType}/{entityId}/reject-fields")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Profile.ADD_TO_REJECTED_FIELD_LIST)
    public void addToRejectedFieldList(@Valid @PathVariable @Size(max = 255) String entityType,
                                       @PathVariable UUID entityId,
                                       @RequestBody RejectFieldRequest request) {
        rejectedFieldsService.addToRejectedFieldList(entityType, entityId, request.fields(), false);
    }

    @DeleteMapping("/{entityType}/{entityId}/reject-fields")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Profile.REMOVE_FROM_REJECTED_FIELD_LIST)
    public void removeFromRejectedFieldList(@Valid @PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId,
                                            @RequestParam("field") Set<String> fields) {
        rejectedFieldsService.removeFromRejectedFieldList(entityType, entityId, fields, false);
    }

    @DeleteMapping("/{entityType}/{entityId}/reject-fields/clear")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Profile.CLEAR_REJECTED_FIELD_LIST)
    public void clearRejectedFieldList(@Valid @PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId) {
        rejectedFieldsService.clearRejectedFieldList(entityType, entityId);
    }

    @GetMapping("/{entityType}/{entityId}/reject-fields")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Profile.GET_REJECTED_FIELD_LIST)
    public RejectFieldResponse getRejectedFieldList(@Valid @PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId) {
        return new RejectFieldResponse(rejectedFieldsService.getRejectedFieldList(entityType, entityId));
    }

    // @formatter:off
    public record RejectFieldRequest(Set<String> fields){}
    public record RejectFieldResponse(Set<String> fields){}
}