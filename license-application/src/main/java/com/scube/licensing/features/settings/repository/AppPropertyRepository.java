package com.scube.licensing.features.settings.repository;

import com.scube.audit.auditable.repositories.AuditableEntityWithoutIDRepository;
import com.scube.licensing.features.settings.entity.AppProperty;
import jakarta.validation.constraints.Size;

import java.util.Optional;
import java.util.UUID;

public interface AppPropertyRepository extends AuditableEntityWithoutIDRepository<AppProperty, UUID> {
    Optional<AppProperty> findByName(@Size(max = 255) String appPropertyName);

    boolean existsByName(@Size(max = 255) String name);

    Optional<AppProperty> findById(UUID id);

    boolean existsById(UUID id);

    void deleteById(UUID id);
}