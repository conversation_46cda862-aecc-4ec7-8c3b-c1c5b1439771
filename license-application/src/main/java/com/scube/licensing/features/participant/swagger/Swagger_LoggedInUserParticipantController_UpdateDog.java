package com.scube.licensing.features.participant.swagger;

import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.annotation.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@SwaggerInfo(summary = "Update dog information", description = "This is used to update the information of a dog in the resident portal. " +
        "The user must be logged in to use this endpoint.")
@Parameter(
        name = "dogEntityId",
        description = "The entity id of the dog to update.",
        example = "12345678-1234-1234-1234-1234567890ab",
        schema = @Schema(implementation = String.class)
)
@Parameter(
        name = "fields",
        description = "The request body should be a map of key value pairs. The key should be the field name and the value should be the new value.",
        example = """
                {
                    "firstName": "<PERSON>",
                    "lastName": "Doe",
                    "dateOfBirth": "1990-01-01"
                }
                """,
        schema = @Schema(implementation = Object.class)
)
@Parameter(
        name = "files",
        description = "The request body should be a map of key value pairs. The key should be the file name and the value should be the file.",
        example = """
                {
                    "dogSpayedOrNeuteredDocument": "(binary)",
                    "dogRabiesVaccinationDocument": "(binary)"
                }
                """,
        schema = @Schema(implementation = Object.class)
)
public @interface Swagger_LoggedInUserParticipantController_UpdateDog {
}
