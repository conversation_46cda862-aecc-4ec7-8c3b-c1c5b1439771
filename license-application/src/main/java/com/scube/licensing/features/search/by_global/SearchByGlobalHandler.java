package com.scube.licensing.features.search.by_global;

import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.utils.DynamicViewUtils;
import lombok.AllArgsConstructor;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@AllArgsConstructor
public class SearchByGlobalHandler implements IRequestHandlerAxon<SearchByGlobalQuery, SearchByGlobalResponse> {
    private final NamedParameterJdbcTemplate jdbcTemplate;


    @Override
    @QueryHandler
    public SearchByGlobalResponse handle(SearchByGlobalQuery command) {
        if (ObjectUtils.isEmpty(command.searchText()))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Search text is required");

        var q = command.searchText().trim();
        String sql = "SELECT * FROM fn_search_global(:q)";
        List<Map<String, Object>> licenses = jdbcTemplate.queryForList(sql, Map.of("q", q));
        if (ObjectUtils.isEmpty(licenses)) return new SearchByGlobalResponse(new ArrayList<>());

        var dupMapList = new HashMap<String, ArrayList<Map<String, Object>>>();
        for (var map : licenses) {
            var entityId = map.get("entityId").toString();
            if (dupMapList.containsKey(entityId)) {
                var list = dupMapList.get(entityId);
                list.add(map);
            } else {
                var list = new ArrayList<Map<String, Object>>();
                list.add(map);
                dupMapList.put(entityId, list);
            }
        }

        var results = new ArrayList<Map<String, Object>>();
        for (var entry : dupMapList.entrySet()) {
            var mapList = entry.getValue();
            var formattedMap = DynamicViewUtils.formatDataList(mapList);
            results.add(formattedMap);
        }

        return new SearchByGlobalResponse(results);
    }
}