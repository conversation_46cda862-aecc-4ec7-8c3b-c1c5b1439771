package com.scube.licensing.features.license.reports.service;

import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.features.license.fee.sql_fees.LicenseFeeService;
import com.scube.licensing.features.license.reports.dto.ReportQueryRequest;
import com.scube.multi.tenant.annotations.NoAsync;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReportService {
    private final LicenseService licenseService;
    private final ReportServiceHelper helper;
    private final LicenseFeeService licenseFeeService;

    @NoAsync
    public List<Map<String, Object>> getLicenseRenewalForm(ReportQueryRequest request, boolean isResident) {
        var startTime = System.currentTimeMillis();
        List<Map<String, Object>> licenseData = licenseService.getLicenseDataDynamic(request.getSql(), request.getParams());

        licenseData = mergeLicenses(licenseData);

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (Map<String, Object> licenseDatum : licenseData) {
            futures.add(helper.recalculateFees(licenseDatum, isResident));
        }

        // Wait for all async operations to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("Time taken to get license renewal form: {} ms", System.currentTimeMillis() - startTime);
        return licenseData;
    }

    public List<Map<String, Object>> getDogLicenseOnlineAnnouncementLetter(ReportQueryRequest query) {
        return licenseService.getLicenseDataDynamic(query.getSql(), query.getParams());
    }

    public List<Map<String, Object>> getAdhocReport(ReportQueryRequest query) {
        return licenseService.getLicenseDataDynamic(query.getSql(), query.getParams());
    }

    public static List<Map<String, Object>> mergeLicenses(List<Map<String, Object>> licenses) {
        // Group by license_id
        Map<Long, Map<String, Object>> mergedMap = new HashMap<>();

        for (Map<String, Object> license : licenses) {
            Long licenseId = (Long) license.get("license_id");

            if (mergedMap.containsKey(licenseId)) {
                // Merge dog lists
                List<Map<String, Object>> existingDogs = (List<Map<String, Object>>) mergedMap.get(licenseId).get("dog");
                List<Map<String, Object>> newDogs = (List<Map<String, Object>>) license.get("dog");
                //loop through newDogs to ensure no duplicates by tagNumber field
                for (Map<String, Object> newDog : newDogs) {
                    boolean exists = existingDogs.stream()
                            .anyMatch(existingDog -> Objects.equals(existingDog.get("tagNumber"), newDog.get("tagNumber")));
                    if (!exists) {
                        existingDogs.add(newDog);
                    }
                }
            } else {
                // Add new entry
                mergedMap.put(licenseId, new HashMap<>(license));
            }
        }

        // Convert map back to list
        return new ArrayList<>(mergedMap.values());
    }
}