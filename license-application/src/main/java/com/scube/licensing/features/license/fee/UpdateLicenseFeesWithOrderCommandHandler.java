package com.scube.licensing.features.license.fee;

import com.scube.licensing.features.license.LicenseService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
@Slf4j
public class UpdateLicenseFeesWithOrderCommandHandler extends FanoutListenerRpc<UpdateLicenseFeesWithOrderCommandHandler.UpdateLicenseFeesWithOrderCommand, Boolean> {
    private final LicenseService licenseService;

    public RabbitResult<Boolean> consume(UpdateLicenseFeesWithOrderCommand event) {
        return RabbitResult.of(() ->
                licenseService.updateLicenseActivityFeeWithOrderId(event.feeIds(), event.orderId())
        );
    }

    // @formatter:off
    public record UpdateLicenseFeesWithOrderCommand(List<Long> feeIds, UUID orderId) implements IRabbitFanoutSubscriberRpc<Boolean> { }
}