package com.scube.licensing.features.license.status;

import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatusCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public final class LicenseStatusResponse {
    private Long id;
    private String name;
    private LicenseStatusCodeEnum code;

    public static LicenseStatusResponse from(LicenseStatus licenseStatus) {
        return LicenseStatusResponse.builder()
                .id(licenseStatus.getId())
                .code(licenseStatus.getCode())
                .name(licenseStatus.getName())
                .build();
    }
}