# License Document Management API

The License Document Management API provides a comprehensive solution for managing documents, including uploading, retrieving, updating, deleting documents, and managing document types. It supports associating documents with different entity types and managing document history.

## Features

- **Upload Documents:** Upload files and associate them with different entities.
- **Retrieve Documents:** Fetch documents associated with specific entities.
- **Document History:** Access the history of documents for auditing purposes.
- **Manage Document Types:** Create and manage different types of documents.\
  <span style="color: red;">!! Document Types must be added through the API otherwise the document history will not function correctly.</span>
- **Delete Documents:** Soft delete documents from the system.

## API Endpoints

| Method | Endpoint                                | Description                        |
|--------|-----------------------------------------|------------------------------------|
| POST   | `/document/{entityType}/{entityId}`     | Upload a document.                 |
| GET    | `/document/{entityType}/{entityId}`     | Get documents for an entity.       |
| GET    | `/document/{documentEntityId}`          | Get a specific document.           |
| GET    | `/document/{documentEntityId}/history`  | Get the history of a document.     |
| DELETE | `/document/{documentEntityId}`          | Delete a specific document.        |
| GET    | `/document/type/{documentTypeKey}`      | Get a specific document type.      |
| GET    | `/document/type`                        | Get all document types.            |
| POST   | `/document/type`                        | Create or update a document type.  |

