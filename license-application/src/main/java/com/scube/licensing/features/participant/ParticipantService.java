package com.scube.licensing.features.participant;

import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.config_utils.json_storage.JsonStorageValue;
import com.scube.lib.misc.dates.DateUtils;
import com.scube.licensing.features.address.service.AddressService;
import com.scube.licensing.features.association.delete_association.DeleteAssociationByEntityCommand;
import com.scube.licensing.features.association.delete_association.DeleteAssociationCommand;
import com.scube.licensing.features.association.find_all_child_associations_by_parent.FindAllChildAssociationsByParentQuery;
import com.scube.licensing.features.association.find_all_parent_associations_by_child.FindAllParentAssociationsByChildQuery;
import com.scube.licensing.features.association.save_association.SaveAssociationCommand;
import com.scube.licensing.features.code_lookup.CodeLookupController;
import com.scube.licensing.features.code_lookup.client.ICodeLookupExchange;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.document.service.DocumentService;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.features.events.Event;
import com.scube.licensing.features.events.axon.AddEvent;
import com.scube.licensing.features.events.dto.EventRequestDto;
import com.scube.licensing.features.events.service.util.EventLicenseFormService;
import com.scube.licensing.features.license.change_status.ChangeStatusHandler;
import com.scube.licensing.features.merge_by_code.dtos.MergeByCodeCommand;
import com.scube.licensing.features.participant.address.ParticipantAddressTypeService;
import com.scube.licensing.features.participant.contact.ContactTypeGroupService;
import com.scube.licensing.features.participant.dto.CreateParticipantRequestDto;
import com.scube.licensing.features.participant.dto.CreateParticipantResponseDTO;
import com.scube.licensing.features.participant.exception.ParticipantNotFoundException;
import com.scube.licensing.features.participant.handlers.save.SaveParticipantCommand;
import com.scube.licensing.features.participant.mapper.ParticipantDtoMapper;
import com.scube.licensing.features.search.specifications.FieldJsonStorage;
import com.scube.licensing.features.search.specifications.SearchSpecification;
import com.scube.licensing.features.participant.type.ParticipantTypeGroupService;
import com.scube.licensing.features.profile.dto.ParticipantDto;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import com.scube.licensing.infrastructure.db.entity.event.EventType;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddress;
import com.scube.licensing.infrastructure.db.entity.participant.contact.Contact;
import com.scube.licensing.infrastructure.db.repository.event.EventTypeRepository;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantRepository;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantStatusRepository;
import com.scube.multi.tenant.TenantContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.dao.CannotAcquireLockException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;
import java.util.stream.Collectors;

import static com.scube.licensing.utils.StringTemplateUtils.removeAnyExtraSpacesAndTrim;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ParticipantService {
    private final ParticipantRepository participantRepository;
    private final ContactTypeGroupService contactTypeGroupService;
    private final ParticipantTypeGroupService participantTypeGroupService;
    private final ParticipantAddressTypeService participantAddressTypeService;
    private final AxonGateway axonGateway;
    private final ParticipantStatusRepository participantStatusRepository;
    private final EventTypeRepository eventTypeRepository;
    private final EventLicenseFormService eventLicenseFormService;
    private final ParticipantDtoMapper participantDtoMapper;
    private final DocumentService documentService;
    private final AddressService addressService;
    private final ICodeLookupExchange codeLookupControllerExchange;

    public void setStatus(Participant participant, String status) {
        var participantStatus = participantStatusRepository.findByParticipantGroupAndNameIgnoreCase(
                        participant.getParticipantTypeGroup().getParticipantGroup(), status)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Participant Status not found"));
        participant.setParticipantStatus(participantStatus);
    }

    @Transactional(readOnly = true)
    public Participant getParticipantOrElseThrowReadOnly(UUID entityId) {
        return participantRepository.findByUuid(entityId)
                .orElseThrow(() -> new ParticipantNotFoundException("Participant with entityId: " + entityId + " not found"));
    }

    public Participant getParticipantOrElseThrow(UUID entityId) {
        return participantRepository.findByUuid(entityId)
                .orElseThrow(() -> new ParticipantNotFoundException("Participant with entityId: " + entityId + " not found"));
    }

    @QueryHandler
    public Optional<Participant> findParticipantByEntityId(FindParticipantByEntityIdQuery query) {
        return participantRepository.findByUuid(query.entityId());
    }

    @QueryHandler
    public List<Participant> findAllParticipants(FindParticipantByEntityIdsQuery query) {
        return participantRepository.findByUuidIn(query.entityIds());
    }

    @QueryHandler
    public Participant findParticipantByIdOrElseThrow(FindParticipantByIdOrElseThrowQuery query) {
        return participantRepository.findById(query.id())
                .orElseThrow(() -> new ParticipantNotFoundException("Participant with id: " + query.id() + " not found"));
    }

    @QueryHandler
    public boolean existsByEntityId(CheckParticipantExistsByEntityIdQuery query) {
        return participantRepository.existsByUuid(query.entityId());
    }

    /**
     * delete a participant
     *
     * @param entityId the entity id of the participant
     * @throws ParticipantNotFoundException if the participant is not found
     */
    public void deleteParticipant(UUID entityId) {
        log.info("Start deleteParticipant with: " + entityId);

        var participant = getParticipantOrElseThrow(entityId);

        deleteAllParticipantAssociations(participant);

        participantRepository.delete(participant);

        log.info("End deleteParticipant...");
    }

    public void deleteAllParticipantAssociations(@NonNull Participant participant, AssociationType... deletedAssociationTypes) {
        if (participant.getId() == null) return;
        //delete associations
        //get all child associations
        var associations = axonGateway.query(new FindAllChildAssociationsByParentQuery(participant));
        //get all parent associations
        associations.addAll(axonGateway.query(new FindAllParentAssociationsByChildQuery(participant)));

        for (Association association : associations) {
            if (ObjectUtils.isEmpty(deletedAssociationTypes)) {
                axonGateway.sendAndWait(new DeleteAssociationCommand(association));
                continue;
            }

            if (List.of(deletedAssociationTypes).contains(association.getChildAssociationType()) ||
                List.of(deletedAssociationTypes).contains(association.getParentAssociationType())) {
                axonGateway.sendAndWait(new DeleteAssociationCommand(association));
            }
        }
    }


    public Participant save(Participant participant) {
        if (participant.getParticipantStatus() == null) setStatus(participant, "Active");
        return participantRepository.save(participant);
    }


    public Participant saveAndFlush(Participant participant) {
        if (participant.getParticipantStatus() == null) setStatus(participant, "Active");
        return participantRepository.saveAndFlush(participant);
    }

    @QueryHandler
    public List<UUID> getSearchDogs(GetSearchDogQuery query) {
        return participantRepository.getSearchDogs(
                query.searchLicenseNumber(), query.searchDogName(), query.searchTagNumber(),
                query.searchBirthYear(), query.searchMicrochipNumber(), query.searchPureBred(),
                query.searchBreed(), query.searchPrimaryColor(), query.searchSecondaryColor(),
                query.searchSex(), query.searchSpayedOrNeutered()
        );
    }

    @QueryHandler
    public List<UUID> getSearchIndividuals(GetSearchIndividualQuery query) {
        return participantRepository.getSearchIndividuals(
                query.searchFirstName(), query.searchLastName(), query.searchEmail(),
                query.searchPhone(), query.searchAddress(), query.searchAddress2(),
                query.searchCity(), query.searchState(), query.searchZip()
        );
    }

    private void patchParticipantAddress(Participant participant, Map<String, Map<String, String>> addressesToUpdate, Map<String, String> request) {
        for (var addr : addressesToUpdate.entrySet()) {
            var addressId = addr.getKey();
            var addressFields = addr.getValue();
            if (addressId != null && addressId.startsWith("new")) {
                // this is so it will create a new address
                addressFields.put("addressType", addressId.replace("new", ""));
            } else {
                addressFields.put("participantAddressId", String.valueOf(addressId));
            }
            axonGateway.sendAndWait(new AddEvent(
                    "individual", participant.getUuid(), "individualAddressChange", new EventRequestDto(addressFields, Map.of())
            ));
        }
        //remove any that starts with address_
        request.keySet().removeIf(x -> x.startsWith("address_"));
    }

    public void patchParticipantContact(Participant participant, Map<String, String> contactFields, Map<String, String> request) {
        patchParticipantContact(participant.getUuid(), contactFields);
        //remove any that starts with contact
        request.keySet().removeIf(x -> x.startsWith("contact"));
    }


    public void patchParticipantContact(UUID entityId, Map<String, String> req) {
        var participant = getParticipantOrElseThrow(entityId);

        var contacts = participant.getContacts();

        var userComment = req.getOrDefault("comment", "");
        req.remove("comment");
        for (var entry : req.entrySet()) {
            var contact = contacts.stream()
                    .filter(c -> String.valueOf(c.getId()).equals(entry.getKey()))
                    .findFirst()
                    .orElseThrow(() -> new ParticipantNotFoundException("fieldName: " + entry.getKey() + " not found"));

            var contactGroup = contact.getContactTypeGroup().getContactGroup().getName();
            var contactType = contact.getContactTypeGroup().getContactType().getName();
            var oldValue = contact.getValue();
            var newValue = entry.getValue();

            if (contactGroup.equalsIgnoreCase("phone")) {
                oldValue = formatPhone(oldValue);
                newValue = formatPhone(newValue);
                contact.setValue(stripPhone(newValue));
            }
            contact.setValue(newValue);

            var action = String.format("%s %s changed from %s to %s", contactType, contactGroup, oldValue, newValue);
            if (ObjectUtils.isEmpty(newValue))
                action = String.format("%s %s removed", contactType, contactGroup);

            if (ObjectUtils.isEmpty(oldValue)) {
                action = String.format("%s %s added with value %s", contactType, contactGroup, newValue);
            }

            if (oldValue.equalsIgnoreCase(newValue)) continue;

            saveEventToProfile(participant, "contactChanged", action, "individual", userComment);
        }

        participantRepository.save(participant);
        eventLicenseFormService.regenerateLicenseForms(participant);
    }

    public String formatPhone(String phone) {
        return phone.replaceAll("(\\d{3})(\\d{3})(\\d+)", "($1) $2-$3");
    }

    public String stripPhone(String phone) {
        return phone.replaceAll("[^\\d]", "");
    }


    public void patchOnlineParticipant(UUID entityId, Map<String, String> fields, Map<String, MultipartFile> files) {
        files = documentService.processFieldToBeDeleted(fields, files);
        Participant participant = getParticipantOrElseThrow(entityId);
        patchParticipant(participant, fields);
        updateApprovalFlags(participant, fields);

        if (!ObjectUtils.isEmpty(files)) {
            // upon update remove rejected fields (RemoveRejectedFieldsDomainEventHandler also removes the rejected fields)
            files.keySet().forEach(key -> {
                participant.rejectedFields().remove(key);
                participant.reviewFields().remove(key);
            });
        }

        participantRepository.save(participant);
        documentService.upload(participant, files);
        eventLicenseFormService.regenerateLicenseForms(participant);
        log.info("End patchOnlineParticipant...");
    }


    public void patchParticipant(UUID entityId, Map<String, String> fields, Map<String, MultipartFile> files) {
        Participant participant = getParticipantOrElseThrow(entityId);
        patchParticipant(participant, fields, files);
    }


    public void patchParticipant(Participant participant, Map<String, String> fields, Map<String, MultipartFile> files) {
        files = documentService.processFieldToBeDeleted(fields, files);
        patchParticipant(participant, fields);
        if (participant.isIndividual()) {
            markIndividualApproval(participant);
        }
        if (participant.isDog()) {
            markDogApproval(participant);
        }

        if (!ObjectUtils.isEmpty(files)) {
            // upon update remove rejected fields (RemoveRejectedFieldsDomainEventHandler also removes the rejected fields)
            files.keySet().forEach(key -> {
                participant.rejectedFields().remove(key);
                participant.reviewFields().remove(key);
            });
        }

        participantRepository.save(participant);
        documentService.upload(participant, files);
        eventLicenseFormService.regenerateLicenseForms(participant);
    }

    private void patchParticipant(Participant participant, Map<String, String> request) {
        if (participant.isIndividual()) {
            var requestUpdate = new CreateParticipantRequestDto(request);
            var optIns = requestUpdate.getOptIns();
            participant.addUpdateOptIn(optIns);
            optIns.keySet().forEach(request::remove);

            //contact updates
            patchParticipantContact(participant, requestUpdate.getContactsToUpdate(), request);

            //address updates
            patchParticipantAddress(participant, requestUpdate.getAddressesToUpdate(), request);
        }

        publishChangedEvent(participant, request);

        for (var entry : request.entrySet()) {
            var fieldName = entry.getKey();
            var value = entry.getValue() != null ? entry.getValue().replaceAll("^\"|\"$", "") : "";

            if (fieldName.equalsIgnoreCase("comment")) continue;

            var type = PropertyTypeEnum.STRING;
            if (fieldName.contains("date")) {
                type = PropertyTypeEnum.DATE;
            } else if (!ObjectUtils.isEmpty(value) && (value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false"))) {
                type = PropertyTypeEnum.BOOLEAN;
            }

            if (fieldName.equalsIgnoreCase("tagNumber") && !ObjectUtils.isEmpty(value)) {
                //reset the old tag number
                participant.getTagNumber().ifPresent(tag -> {
                    codeLookupControllerExchange.updateTag(new CodeLookupController.TagUpdateRequest(tag, "tag",
                            null, CodeLookupActionEnum.CREATE, TenantContext.getTenantId()));
                });
                // make the new tag number as a lookup
                codeLookupControllerExchange.updateTag(new CodeLookupController.TagUpdateRequest(value, "dog",
                        participant.getUuid().toString(), CodeLookupActionEnum.LOOKUP, TenantContext.getTenantId()));
            }

            participant.setProperty(type, fieldName, value);
        }
    }

    public void updateApprovalFlags(Participant participant, Map<String, String> map) {
        if (participant.isIndividual()) {
            var nameKeys = List.of("title", "firstName", "middleName", "lastName", "suffix", "dateOfBirth");
            var isBasicInfoChanged = map.keySet().stream().anyMatch(nameKeys::contains);
            if (isBasicInfoChanged) {
                participant.setProperty(PropertyTypeEnum.BOOLEAN, "basicInfoApproved", false);
            }

            var addressKeys = List.of("address", "address2", "city", "state", "zip", "streetAddress", "streetAddress2");
            var isAddressChanged = map.keySet().stream().anyMatch(addressKeys::contains);
            if (isAddressChanged) {
                participant.setProperty(PropertyTypeEnum.BOOLEAN, "addressApproved", false);
            }
        } else if (participant.isDog()) {
            List<String> behaviorUpdatedFields = List.of("dogFriendly", "catFriendly", "childFriendly", "isDangerous");

            List<String> basicInfoUpdatedFields = List.of("dateOfBirth", "dogBio", "dogBirthDate", "dogName", "dogRecId",
                    "dogSex", "dogSpayedOrNeutered", "microchipNumber", "registryAssociation", "registryNumber", "tagNumber");

            List<String> physicalCharacteristicsUpdatedFields = List.of("dogMarkings", "dogPrimaryColor", "dogSecondaryColor", "dogBreed");

            List<String> vaccineUpdatedFields = List.of("rabiesTagNumber", "vaccineAdministeredDate", "vaccineBrand", "vaccineDatesExempt",
                    "vaccineDueDate", "vaccineLotExpirationDate", "vaccineLotNumber", "vaccineName", "vaccinePeriod", "vaccineProducer", "veterinarianName", "veterinaryName"
            );

            var insuranceFields = List.of("insuranceCompany", "insurancePolicyNumber", "insuranceStartDate", "insuranceEndDate");

            var isBasicInfoChanged = map.keySet().stream().anyMatch(basicInfoUpdatedFields::contains);
            if (isBasicInfoChanged) {
                participant.setProperty(PropertyTypeEnum.BOOLEAN, "basicInfoApproved", false);
            }

            var isBehaviorChanged = map.keySet().stream().anyMatch(behaviorUpdatedFields::contains);
            if (isBehaviorChanged) {
                participant.setProperty(PropertyTypeEnum.BOOLEAN, "behaviorApproved", false);
            }

            var isPhysicalCharacteristicsChanged = map.keySet().stream().anyMatch(physicalCharacteristicsUpdatedFields::contains);
            if (isPhysicalCharacteristicsChanged) {
                participant.setProperty(PropertyTypeEnum.BOOLEAN, "physicalCharacteristicsApproved", false);
            }

            var isVaccineChanged = map.keySet().stream().anyMatch(vaccineUpdatedFields::contains);
            if (isVaccineChanged) {
                participant.setProperty(PropertyTypeEnum.BOOLEAN, "vaccineApproved", false);
            }

            var isInsuranceChanged = map.keySet().stream().anyMatch(insuranceFields::contains);
            if (isInsuranceChanged) {
                participant.setProperty(PropertyTypeEnum.BOOLEAN, "insuranceApproved", false);
            }
        }
    }

    public void publishChangedEvent(Participant participant, Map<String, String> newFields) {
        var oldFields = participant.getProperties();

        var userComment = newFields.getOrDefault("comment", "");
        newFields.remove("comment");

        if (participant.isIndividual()) {

            var nameKeys = List.of("title", "firstName", "middleName", "lastName", "suffix");
            if (nameKeys.stream().anyMatch(newFields::containsKey)) {
                var oldFullName = removeAnyExtraSpacesAndTrim(oldFields.getOrDefault("title", "") + " " + oldFields.getOrDefault("firstName", "") + " " + oldFields.getOrDefault("middleName", "") + " " + oldFields.getOrDefault("lastName", "") + " " + oldFields.getOrDefault("suffix", ""));
                var newFullName = removeAnyExtraSpacesAndTrim(oldFields.getOrDefault("title", "") + " " + newFields.getOrDefault("firstName", "") + " " + newFields.getOrDefault("middleName", "") + " " + newFields.getOrDefault("lastName", "") + " " + oldFields.getOrDefault("suffix", ""));
                String action = "Name changed from " + oldFullName + " to " + newFullName;
                if (ObjectUtils.isEmpty(oldFullName) || oldFullName.equalsIgnoreCase(newFullName)) {
                    action = "Name changed to " + newFullName;
                }
                saveEventToProfile(participant, "nameChanged", action, "individual", userComment);
            }

            var dobKeys = List.of("dateOfBirth");
            if (dobKeys.stream().anyMatch(newFields::containsKey)) {
                var oldDate = oldFields.getOrDefault("dateOfBirth", "");
                if (oldDate == null) oldDate = "";
                Object newDate = newFields.getOrDefault("dateOfBirth", "");

                var oldDateTime = DateUtils.toDateTime(oldDate.toString());
                var newDateTime = DateUtils.toDateTime(newDate.toString());
                oldDate = oldDateTime == null ? "" : oldDateTime.toLocalDate();
                newDate = newDateTime == null ? "" : newDateTime.toLocalDate();
                String action = "Date of Birth changed from " + oldDate + " to " + newDate;
                if (ObjectUtils.isEmpty(oldDate) || oldDate.equals(newDate)) {
                    action = "Date of Birth changed to " + newDate;
                }
                saveEventToProfile(participant, "dateOfBirthChanged", action, "individual", userComment);
            }

            var addressKeys = List.of("address", "address2", "city", "state", "zip");
            Map<String, String> addressFields = newFields.keySet().stream()
                    .filter(addressKeys::contains)
                    .collect(HashMap::new, (m, k) -> m.put(k, newFields.get(k)), HashMap::putAll);
            if (!ObjectUtils.isEmpty(addressFields)) {
                var oldAddress = removeAnyExtraSpacesAndTrim(oldFields.getOrDefault("address", "") + ", " + oldFields.getOrDefault("address2", "") + ", " + oldFields.getOrDefault("city", "") + ", " + oldFields.getOrDefault("state", "") + " " + oldFields.getOrDefault("zip", ""));
                var newAddress = removeAnyExtraSpacesAndTrim(newFields.getOrDefault("address", "") + ", " + newFields.getOrDefault("address2", "") + ", " + newFields.getOrDefault("city", "") + ", " + newFields.getOrDefault("state", "") + " " + newFields.getOrDefault("zip", ""));
                String action = "Address changed from " + oldAddress + " to " + newAddress;
                if (ObjectUtils.isEmpty(oldAddress) || oldAddress.equalsIgnoreCase(newAddress)) {
                    action = "Address changed to " + newAddress;
                }
                saveEventToProfile(participant, "individualAddressChange", action, "individual", userComment);
            }

            var contactKeys = List.of("email", "phone");
            Map<String, String> contactFields = newFields.keySet().stream()
                    .filter(contactKeys::contains)
                    .collect(HashMap::new, (m, k) -> m.put(k, newFields.get(k)), HashMap::putAll);
            if (!ObjectUtils.isEmpty(contactFields)) {
                for (var contactField : contactFields.entrySet()) {
                    var newContact = contactField.getValue();
                    var key = contactField.getKey();
                    if (key.equalsIgnoreCase("email")) {
                        String action = "Email changed to " + newContact;
                        saveEventToProfile(participant, "contactChanged", action, "individual", userComment);
                    }
                    if (key.equalsIgnoreCase("phone")) {
                        //format phone like (*************
                        var formattedPhone = stripPhone(newContact).replaceAll("(\\d{3})(\\d{3})(\\d+)", "($1) $2-$3");
                        String action = "Phone changed to " + formattedPhone;
                        saveEventToProfile(participant, "contactChanged", action, "individual", userComment);
                    }
                }
            }

            //all others

            Map<String, String> remainingFields = newFields.keySet().stream()
                    .filter(x -> !nameKeys.contains(x) && !addressKeys.contains(x) && !contactKeys.contains(x) && !dobKeys.contains(x))
                    .collect(HashMap::new, (m, k) -> m.put(k, newFields.get(k)), HashMap::putAll);
            if (!remainingFields.isEmpty()) {
                String action = buildComment(remainingFields, oldFields);

                saveEventToProfile(participant, "individualChanged", action, "individual", userComment);
            }
        } else if (participant.isDog()) {
            var isVaccineChanged = false;
            //check if any the keys in the newFields contains vaccine
            isVaccineChanged = newFields.keySet().stream().anyMatch(x -> x.contains("vaccine"));
            if (isVaccineChanged) {
                String action = buildComment(newFields, oldFields);
                saveEventToProfile(participant, "vaccineChanged", action, "dog", userComment);
            } else {
                String action = buildComment(newFields, oldFields);
                saveEventToProfile(participant, "dogChanged", action, "dog", userComment);
            }
        }
    }

    public void saveEventToProfile(Participant participant, String eventTypeCode, String action, String entityType, String userComment) {
        EventType eventType = eventTypeRepository.findByCode(eventTypeCode)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Event type " + eventTypeCode + " not found"));

        if (!eventType.getProfileType().getName().equalsIgnoreCase(entityType)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid entity type " + entityType + " for event type " + eventTypeCode);
        }

        participant.addEvent(new Event(eventType, userComment, action));

        axonGateway.sendAndWait(new SaveParticipantCommand(participant));

        axonGateway.sendAndWait(new ChangeStatusHandler.ChangeAllLicenseModifiersCommand(participant, "License Correction"));

        eventLicenseFormService.regenerateLicenseForms(participant);
    }

    private String buildComment(Map<String, String> newFields, Map<String, Object> oldFields) {
        return newFields.entrySet().stream()
                .map(x -> {
                    var key = x.getKey();
                    var newValue = ObjectUtils.isEmpty(x.getValue()) ? "" : x.getValue();
                    var oldValue = oldFields.getOrDefault(key, "") == null ? "" : oldFields.getOrDefault(key, "");
                    if (newValue.equals(oldValue)) return "";
                    if (ObjectUtils.isEmpty(oldValue)) {
                        return key + " changed to " + removeAnyExtraSpacesAndTrim(newValue);
                    }
                    return key + " changed from " + removeAnyExtraSpacesAndTrim(oldValue + " to " + newValue);
                })
                .filter(x -> !ObjectUtils.isEmpty(x))
                .collect(Collectors.joining(", "));
    }

    public ParticipantDto toDto(Participant participant) {
        return participantDtoMapper.toDto(participant);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    @Retryable(retryFor = CannotAcquireLockException.class, maxAttempts = 2, backoff = @Backoff(delay = 1000))
    public CreateParticipantResponseDTO createResident(Map<String, String> fields, Map<String, MultipartFile> files) {
        log.info("Start createResident with: {}", fields);
        files = documentService.processFieldToBeDeleted(fields, files);
        var requestDto = new CreateParticipantRequestDto(fields);

        var participant = new Participant();

        if (checkIfResidentExists(requestDto.getEmail())) {
            log.error("Resident with email {} already exists", requestDto.getEmail());
            // the resident already exists, so we'll update the existing resident
            var existingResident = axonGateway.query(new GetSearchIndividualQuery(requestDto.getEmail())).getFirst();
            participant = getParticipantOrElseThrow(existingResident);
        }

        if (ObjectUtils.isEmpty(requestDto.getAddresses())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Cannot create a resident without an address");
        }

        var result = putUpdateResident(participant, requestDto);

        List<DocumentDto> documents = documentService.upload(participant, files);
        result.setDocuments(documents);

        markIndividualApproval(participant);

        log.info("End createResident...");
        return result;
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    @Retryable(retryFor = CannotAcquireLockException.class, maxAttempts = 2, backoff = @Backoff(delay = 1000))
    public CreateParticipantResponseDTO createOnlineResident(UUID participantEntityId, Map<String, String> fields, Map<String, MultipartFile> files) {
        log.info("Start createOnlineResident with participantEntityId {} and jsonBodyMap {}", participantEntityId, fields);
        files = documentService.processFieldToBeDeleted(fields, files);
        var requestDto = new CreateParticipantRequestDto(fields);
        if (ObjectUtils.isEmpty(requestDto.getAddresses())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Cannot create a resident without an address");
        }

        Participant participant = findParticipantByEntityId(new FindParticipantByEntityIdQuery(participantEntityId))
                .orElse(new Participant());
        participant.setUuid(participantEntityId);
        participant.setRegistered(true);


        CreateParticipantResponseDTO result = putUpdateResident(participant, requestDto);
        List<DocumentDto> documents = documentService.upload(participant, files);
        result.setDocuments(documents);

        updateApprovalFlags(participant, fields);

        participant.setRegistrationCode(null);
        if (requestDto.isRegistrationCodePresent()) {
            axonGateway.sendAndWait(new MergeByCodeCommand(participantEntityId, requestDto.getRegistrationCode()));
        }


        log.info("End createOnlineResident...");
        return result;
    }

    public boolean checkIfResidentExists(String email) {
        var uuids = axonGateway.query(new GetSearchIndividualQuery(email));
        return !ObjectUtils.isEmpty(uuids);
    }


    public void martResidentAsOnline(UUID participantEntityId) {
        log.info("Start martResidentAsOnline with participantEntityId {}", participantEntityId);
        Participant participant = getParticipantOrElseThrow(participantEntityId);
        participant.setRegistered(true);
        participant.setRegistrationCode(null);
        save(participant);
        log.info("End martResidentAsOnline...");
    }

    public void martResidentAsOffline(UUID participantEntityId) {
        log.info("Start martResidentAsOffline with participantEntityId {}", participantEntityId);
        Participant participant = getParticipantOrElseThrow(participantEntityId);
        participant.setRegistered(false);
        save(participant);
        log.info("End martResidentAsOffline...");
    }

    public void markAllAsApproved(UUID... participantEntityIds) {
        for (var participantEntityId : participantEntityIds) {
            log.info("Start martResidentAsApproved with participantEntityId {}", participantEntityId);
            Participant participant = getParticipantOrElseThrow(participantEntityId);
            participant.markAsApproved();
            if (participant.isIndividual())
                markIndividualApproval(participant);
            else if (participant.isDog())
                markDogApproval(participant);
            save(participant);
            log.info("End martResidentAsApproved...");
        }
    }

    public void clearAllApproval(UUID participantEntityId) {
        log.info("Start martResidentAsUnapproved with participantEntityId {}", participantEntityId);
        Participant participant = getParticipantOrElseThrow(participantEntityId);
        participant.clearApproval();
        if (participant.isIndividual())
            clearIndividualApproval(participant);
        else if (participant.isDog())
            clearDogApproval(participant);
        save(participant);
        log.info("End martResidentAsUnapproved...");
    }

    public CreateParticipantResponseDTO putUpdateResident(@NonNull Participant participant, CreateParticipantRequestDto requestDto) {
        // remove all the contacts
        participant.getContacts().clear();
        //remove all the addresses
        participant.getParticipantAddresses().clear();

        // delete all the participant associations
        deleteAllParticipantAssociations(participant, AssociationType.ADDRESS);

        var participantTypeGroup = participantTypeGroupService.getParticipantTypeGroup("License Holder", "Individual");
        participant.setParticipantTypeGroup(participantTypeGroup);

        for (var contact : requestDto.getContacts()) {
            var contactTypeGroup = contactTypeGroupService.getContactTypeGroup(contact.getType(), contact.getGroup());
            var newContact = new Contact().setValue(contact.getValue());
            participant.addContact(newContact, contactTypeGroup);
        }

        for (var addr : requestDto.getAddresses()) {
            var participantAddressType = participantAddressTypeService.getParticipantAddressType(addr.getType());
            var address = addr.getAddress();
            if (ObjectUtils.isEmpty(address.getStreetAddress())) continue;

            address = addressService.geocodeAddress(address);
            participant.addAddress(address, participantAddressType);
        }

        for (var custom : requestDto.getCustomFields()) {
            var type = PropertyTypeEnum.fromValue(custom.getType());
            participant.setProperty(type, custom.getKey(), custom.getValue());
        }

        for (EntityAssociation assoc : requestDto.getAssociations()) {
            participant.addBiDirectionalAssociable(assoc.getEntityType(), assoc.getEntityId());
        }

        // any optIn fields
        participant.addUpdateOptIn(requestDto.getOptIns());

        participant = save(participant);

        //associate participant with address
        for (var address : participant.getParticipantAddresses().stream().map(ParticipantAddress::getAddress).toList()) {
            axonGateway.sendAndWait(new SaveAssociationCommand(participant, address));
            axonGateway.sendAndWait(new SaveAssociationCommand(address, participant));
        }

        var entityId = participant.getUuid();
        var participantGroup = participant.getParticipantTypeGroup().getParticipantGroup().getName();

        publishChangedEvent(participant, requestDto.toStringMap());

        return new CreateParticipantResponseDTO(entityId, participantGroup);
    }

    public List<Participant> findAllById(Iterable<Long> dogs) {
        return participantRepository.findAllById(dogs);
    }

    public void markApproval(UUID participantEntityId, String fieldName) {
        log.info("Start markApproval with participantEntityId {} and fieldName {}", participantEntityId, fieldName);
        Participant participant = getParticipantOrElseThrow(participantEntityId);
        participant.setProperty(PropertyTypeEnum.BOOLEAN, fieldName, true);
        log.info("End markApproval...");
    }

    public void markApproval(Participant participant, String fieldName) {
        log.info("Start markApproval with participantEntityId {} and fieldName {}", participant.getUuid(), fieldName);
        participant.setProperty(PropertyTypeEnum.BOOLEAN, fieldName, true);
        log.info("End markApproval...");
    }

    public void clearAllApproval(UUID participantEntityId, String fieldName) {
        log.info("Start markApproval with participantEntityId {} and fieldName {}", participantEntityId, fieldName);
        Participant participant = getParticipantOrElseThrow(participantEntityId);
        participant.setProperty(PropertyTypeEnum.BOOLEAN, fieldName, false);
        log.info("End markApproval...");
    }

    public void clearAllApproval(Participant participant, String fieldName) {
        log.info("Start markApproval with participantEntityId {} and fieldName {}", participant.getUuid(), fieldName);
        participant.setProperty(PropertyTypeEnum.BOOLEAN, fieldName, false);
        log.info("End markApproval...");
    }

    public void markDogApproval(Participant participant) {
        markApproval(participant, "basicInfoApproved");
        markApproval(participant, "behaviorApproved");
        markApproval(participant, "physicalCharacteristicsApproved");
        markApproval(participant, "vaccineApproved");
        markApproval(participant, "insuranceApproved");
    }

    public void markIndividualApproval(Participant participant) {
        markApproval(participant, "basicInfoApproved");
        markApproval(participant, "addressApproved");
    }

    public void clearDogApproval(Participant participant) {
        clearAllApproval(participant, "basicInfoApproved");
        clearAllApproval(participant, "behaviorApproved");
        clearAllApproval(participant, "physicalCharacteristicsApproved");
        clearAllApproval(participant, "vaccineApproved");
        clearAllApproval(participant, "insuranceApproved");
    }

    public void clearIndividualApproval(Participant participant) {
        clearAllApproval(participant, "basicInfoApproved");
        clearAllApproval(participant, "addressApproved");
    }

    public void transferOwnerShip(Participant fromParticipant, Participant toParticipant) {
        if (fromParticipant.getUuid().equals(toParticipant.getUuid())) return;

        List<License> licenses = fromParticipant.getLicenses();
        List<Participant> dogs = fromParticipant.getAssociatedParticipants();
        List<Document> documents = fromParticipant.getDocuments();
        List<Event> events = fromParticipant.getEvents();

        if (licenses != null)
            licenses.forEach(license -> associationTransfer(fromParticipant, toParticipant, license));
        if (dogs != null)
            dogs.forEach(dog -> associationTransfer(fromParticipant, toParticipant, dog));
        if (documents != null)
            documents.forEach(document -> associationTransfer(fromParticipant, toParticipant, document));
        if (events != null)
            events.forEach(toParticipant::addEvent);

        //delete the from participant
        deleteParticipant(fromParticipant.getUuid());
    }

    private void associationTransfer(Participant fromParticipant, Participant toParticipant, Associable associable) {
        axonGateway.sendAndWait(new SaveAssociationCommand(toParticipant, associable));
        axonGateway.sendAndWait(new SaveAssociationCommand(associable, toParticipant));

        axonGateway.sendAndWait(new DeleteAssociationByEntityCommand(fromParticipant, associable));
        axonGateway.sendAndWait(new DeleteAssociationByEntityCommand(associable, fromParticipant));
    }

    @JsonStorageValue({"config=fieldSpecification"})
    private FieldJsonStorage fieldJsonStorage;

    @Transactional(readOnly = true)
    public Page<?> query(Pageable pageable, Map<String, Object> searchParams) {
        Page<Participant> participants = participantRepository.findAll(new SearchSpecification<>(searchParams, fieldJsonStorage), pageable);
        return participants.map(participantDtoMapper::toDto);
    }

    public List<Participant> getImpoundedDogNotNotifiedAlready() {
        var searchParams = new HashMap<String, Object>();
        searchParams.put("status", "Impounded");
        searchParams.put("dogImpoundmentNotified", false);
        return participantRepository.findAll(new SearchSpecification<>(searchParams, fieldJsonStorage));
    }

    // @formatter:off
    public record FindParticipantByEntityIdQuery(UUID entityId) implements IRequestAxon<Optional<Participant>> { }
    public record FindParticipantByEntityIdsQuery(Iterable<UUID> entityIds) implements IRequestAxon<List<Participant>> { }
    public record FindParticipantByIdOrElseThrowQuery(Long id) implements IRequestAxon<Participant> { }
    public record CheckParticipantExistsByEntityIdQuery(UUID entityId) implements IRequestAxon<Boolean> { }
    public record GetSearchDogQuery(
            String searchLicenseNumber, String searchDogName, String searchTagNumber, String searchBirthYear,
            String searchMicrochipNumber, String searchPureBred, String searchBreed, String searchPrimaryColor,
            String searchSecondaryColor, String searchSex, String searchSpayedOrNeutered
    ) implements IRequestAxon<List<UUID>> { }
    public record GetSearchIndividualQuery(
            String searchFirstName, String searchLastName, String searchEmail, String searchPhone,
            String searchAddress, String searchAddress2, String searchCity, String searchState, String searchZip
    ) implements IRequestAxon<List<UUID>> {
        public GetSearchIndividualQuery(String searchEmail) {
            this(null, null, searchEmail, null, null, null, null, null, null);
        }
    }
}