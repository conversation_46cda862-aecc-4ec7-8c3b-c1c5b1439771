package com.scube.licensing.infrastructure.db.entity.tenant;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(name = Tenant.TABLE_NAME)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class Tenant extends BaseEntity {
    public static final String TABLE_NAME = "tenant";
    public static final String ID = "tenant_id";
    public static final String TEMPLATE_KEY = "template_key";
    public static final String CLERK_SIGNATURE = "clerk_signature";
    public static final String CLERK_NAME = "clerk_name";
    public static final String CLERK_TITLE = "clerk_title";
    public static final String CITY_CLERK_OFFICE_NAME = "city_clerk_office_name";
    public static final String CLERK_EMAIL = "clerk_email";
    public static final String CLERK_PHONE_NUMBER = "clerk_phone_number";
    public static final String ADMIN_OFFICE = "admin_office";
    public static final String ADMIN_OFFICE_ROOM = "admin_office_room";
    public static final String ADMIN_STREET = "admin_Street";
    public static final String ADMIN_CITY = "admin_city";
    public static final String ADMIN_STATE = "admin_state";
    public static final String ADMIN_ZIP_CODE = "admin_zip_code";
    public static final String ENTITY_ID = "entity_id";

    @Size(max = 255)
    @Column(name = TEMPLATE_KEY, nullable = false, unique = true)
    private String templateKey;

    @Size(max = 255)
    @Column(name = CLERK_SIGNATURE)
    private String clerkSignature;

    @Size(max = 255)
    @Column(name = CLERK_NAME)
    private String clerkName;

    @Size(max = 255)
    @Column(name = CLERK_TITLE)
    private String clerkTitle;

    @Size(max = 255)
    @Column(name = CITY_CLERK_OFFICE_NAME)
    private String cityClerkOfficeName;

    @Size(max = 255)
    @Column(name = CLERK_EMAIL)
    private String clerkEmail;

    @Size(max = 255)
    @Column(name = CLERK_PHONE_NUMBER)
    private String clerkPhoneNumber;

    @Size(max = 255)
    @Column(name = ADMIN_OFFICE)
    private String adminOffice;

    @Size(max = 255)
    @Column(name = ADMIN_OFFICE_ROOM)
    private String adminOfficeRoom;

    @Size(max = 500)
    @Column(name = ADMIN_STREET, length = 500)
    private String adminStreet;

    @Size(max = 255)
    @Column(name = ADMIN_CITY)
    private String adminCity;

    @Size(max = 255)
    @Column(name = ADMIN_STATE)
    private String adminState;

    @Size(max = 255)
    @Column(name = ADMIN_ZIP_CODE)
    private String adminZipCode;

    @Size(max = 255)
    private String clerkXpressUrl;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}