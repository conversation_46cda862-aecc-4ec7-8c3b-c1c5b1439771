package com.scube.licensing.infrastructure.db.repository.participant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.participant.contact.ContactType;
import jakarta.validation.constraints.Size;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ContactTypeRepository extends AuditableEntityRepository<ContactType, Long> {
    Optional<ContactType> findByNameIgnoreCase(@Size(max = 255) String name);
}