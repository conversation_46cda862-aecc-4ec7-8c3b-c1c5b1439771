package com.scube.licensing.infrastructure.db.entity.custom_entity;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = CustomEntityType.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class CustomEntityType extends BaseEntity {
    public static final String TABLE_NAME = "custom_entity_type";
    public static final String CUSTOM_ENTITY_TYPE_ID = "custom_entity_type_id";

    @Size(max = 50)
    @Column(length = 50)
    private String name;

    @Size(max = 250)
    @Column(length = 250)
    private String description;

    @OneToMany(
            mappedBy = "customEntityType",
            cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST},
            orphanRemoval = true
    )
    private Set<CustomEntitySubType> customEntitySubTypes = new HashSet<>();

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}