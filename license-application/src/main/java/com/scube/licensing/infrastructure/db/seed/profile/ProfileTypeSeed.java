package com.scube.licensing.infrastructure.db.seed.profile;

import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.db.entity.profile_builder.ProfileType;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ProfileTypeSeed {
    private final ProfileService profileService;

    @Transactional
    public void seed() {
        log.info("Start seeding ProfileTypeSeed.seed()...");

        var types = List.of(
                new ProfileTypeSeedModel("individual", "individual profile type"),
                //new ProfileTypeSeedModel("business", "business profile type", "businessUserProfile"),
                new ProfileTypeSeedModel("dog", "dog profile type"),
                new ProfileTypeSeedModel("parcel", "parcel profile type"),
                new ProfileTypeSeedModel("license", "license profile type")
        );

        types.forEach(type -> seedProfile(type.name(), type.description()));

        log.info("End seeding ProfileTypeSeed.seed()...");
    }

    private void seedProfile(String name, String description) {
        if (profileService.checkProfileTypeExistsByName(name)) {
            log.info("ProfileTypeSeed.seed() {} already seeded", name);
            return;
        }

        var profileType = new ProfileType(name, description);
        profileService.saveType(profileType);
    }

    public record ProfileTypeSeedModel(String name, String description) {
    }
}