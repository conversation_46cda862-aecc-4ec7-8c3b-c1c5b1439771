package com.scube.licensing.utils.code_generator;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum CGOptions {
    LOWER_ALPHABET,
    UPPER_ALPHABET,
    NUMBERS,
    LOWER_UPPER_ALPHABET,
    LOWER_ALPHABET_NUMBERS,
    UPPER_ALPHABET_NUMBERS,
    LOWER_UPPER_ALPHABET_NUMBERS;

    public static List<CGOptions> normalize(CGOptions... options) {
        return Arrays.stream(options)
                .map(x -> x.normalize())
                .flatMap(List::stream)
                .toList();
    }

    public List<CGOptions> normalize() {
        return normalize(this);
    }

    public static List<CGOptions> normalize(CGOptions option) {
        List<CGOptions> normalized = new ArrayList<>();
        switch (option) {
            case LOWER_UPPER_ALPHABET -> {
                normalized.add(CGOptions.LOWER_ALPHABET);
                normalized.add(CGOptions.UPPER_ALPHABET);
            }
            case LOWER_ALPHABET_NUMBERS -> {
                normalized.add(CGOptions.NUMBERS);
                normalized.add(CGOptions.LOWER_ALPHABET);
                normalized.add(CGOptions.NUMBERS);
            }
            case UPPER_ALPHABET_NUMBERS -> {
                normalized.add(CGOptions.NUMBERS);
                normalized.add(CGOptions.UPPER_ALPHABET);
                normalized.add(CGOptions.NUMBERS);
            }
            case LOWER_UPPER_ALPHABET_NUMBERS -> {
                normalized.add(CGOptions.NUMBERS);
                normalized.add(CGOptions.LOWER_ALPHABET);
                normalized.add(CGOptions.NUMBERS);
                normalized.add(CGOptions.UPPER_ALPHABET);
                normalized.add(CGOptions.NUMBERS);
            }
            default -> normalized.add(option);
        }
        return normalized;
    }
}