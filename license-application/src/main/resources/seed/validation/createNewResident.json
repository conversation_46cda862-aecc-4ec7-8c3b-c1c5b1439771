[{"tableName": "participant", "name": "firstName", "type": "string", "notNull": true, "max": 50, "min": 1}, {"tableName": "participant", "name": "middleName", "type": "string", "max": 50}, {"tableName": "participant", "name": "lastName", "type": "string", "notNull": true, "max": 50, "min": 1}, {"tableName": "participant", "name": "suffix", "type": "string", "max": 10}, {"tableName": "participant", "name": "title", "type": "string", "max": 10}, {"tableName": "participant", "name": "dateOfBirth", "type": "date", "ageMin": 18, "ageMax": 120, "past": true}, {"tableName": "participant", "name": "email", "type": "email", "max": 100}, {"tableName": "participant", "name": "phone", "type": "phoneStrip", "max": 50}, {"tableName": "participant", "name": "workPhone", "type": "phoneStrip", "max": 50}, {"tableName": "participant", "name": "fax", "type": "phoneStrip", "max": 50}, {"tableName": "participant", "name": "cell", "type": "phoneStrip", "max": 50}, {"tableName": "participant", "name": "otherPhone", "type": "phoneStrip", "max": 50}, {"tableName": "participant", "name": "address", "type": "string", "max": 100}, {"tableName": "participant", "name": "address2", "type": "string", "max": 100}, {"tableName": "participant", "name": "city", "type": "string", "max": 50}, {"tableName": "participant", "name": "state", "type": "string", "pattern": "^[A-Z]{2}$"}, {"tableName": "participant", "name": "zip", "type": "string", "max": 50}, {"tableName": "participant", "name": "mailAddress", "type": "string", "max": 100}, {"tableName": "participant", "name": "mailAddress2", "type": "string", "max": 100}, {"tableName": "participant", "name": "mailCity", "type": "string", "max": 50}, {"tableName": "participant", "name": "mailState", "type": "string", "pattern": "^[A-Z]{2}$"}, {"tableName": "participant", "name": "mailZip", "type": "string", "max": 50}, {"tableName": "participant", "name": "workAddress", "type": "string", "max": 100}, {"tableName": "participant", "name": "workAddress2", "type": "string", "max": 100}, {"tableName": "participant", "name": "workCity", "type": "string", "max": 50}, {"tableName": "participant", "name": "workState", "type": "string", "pattern": "^[A-Z]{2}$"}, {"tableName": "participant", "name": "workZip", "type": "string", "max": 50}]