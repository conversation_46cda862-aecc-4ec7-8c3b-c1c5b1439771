
CREATE OR REPLACE FUNCTION create_event(
    fn_entityType TEXT,
    fn_entityId UUID,
    fn_eventTypeId BIGINT,
    fn_action TEXT,
    fn_createdBy TEXT
) RETURNS VOID AS $$
DECLARE
    new_event JSONB;
BEGIN
    -- call the create_event function with the comment parameter set to NULL
    PERFORM create_event(fn_entityType, fn_entityId, fn_eventTypeId, fn_action, NULL, fn_createdBy, now());

END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION create_event(
    fn_entityType TEXT,
    fn_entityId UUID,
    fn_eventTypeId BIGINT,
    fn_action TEXT,
    fn_comment TEXT,
    fn_createdBy TEXT,
    fn_createdDate timestamptz
) RETURNS VOID AS $$
DECLARE
    new_event JSONB;
BEGIN
    -- Build the new event JSON object without comment
    new_event := jsonb_build_object(
        'uuid', gen_random_uuid(),
        'action', fn_action,
        'comment', fn_comment,
        'createdBy', fn_createdBy,
        'inherited', false,
        'createdDate', fn_createdDate,
        'eventTypeId', fn_eventTypeId
    );

    -- Conditional logic for entity types
    IF fn_entityType = 'license' THEN
        UPDATE license
        SET events = events || new_event
        WHERE license_uuid = fn_entityId;

    ELSIF fn_entityType = 'participant' THEN
        UPDATE participant
        SET events = events || new_event
        WHERE participant_uuid = fn_entityId;

    ELSE
        RAISE EXCEPTION 'Unsupported entity type: %', fn_entityType;
    END IF;
END;
$$ LANGUAGE plpgsql;


--select * from create_event('license', '64e35403-3e42-4c00-b4d5-25c5967fa06e', 25, 'action', 'createdBy');
