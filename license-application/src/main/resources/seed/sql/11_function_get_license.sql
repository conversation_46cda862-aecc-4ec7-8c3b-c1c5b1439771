CREATE OR REPLACE FUNCTION get_licenses (
    IN participantEntityId varchar,
    IN participantGroupType varchar
)
RETURNS TABLE (
    licenseEntityId varchar
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    select l.license_uuid as "licenseEntityId" from view_participant vp
    	LEFT JOIN association assoc on vp.participant_id = assoc.parent_id
    	LEFT JOIN license l on assoc.child_id = l.license_id
    	WHERE assoc.parent_association_type = 'PARTICIPANT'
            AND assoc.child_association_type = 'LICENSE'
    	    AND (coalesce(participantGroupType,'') = '' OR group_name ILIKE participantGroupType)
            AND (coalesce(participantEntityId,'') = '' OR vp.entity_id ILIKE participantEntityId);
END;
$$;