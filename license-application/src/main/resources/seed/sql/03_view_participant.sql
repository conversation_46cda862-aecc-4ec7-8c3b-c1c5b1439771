CREATE OR REPLACE VIEW view_participant AS
SELECT
	  p.participant_id,
	  p.participant_uuid as entity_id,
	  p.name as participant_name,
	  pg.name as group_name,
	  pt.name as type_name,
	  ps.name as status_name,
	  p.properties
from participant p

	left join participant_type_group ptg
		on ptg.participant_type_group_id = p.participant_type_group_id

	left join participant_group pg
		on pg.participant_group_id = ptg.participant_group_id

	left join participant_type pt
		on pt.participant_type_id = ptg.participant_type_id

	left join participant_status ps
        on ps.participant_status_id = p.participant_status_id;

--SELECT * FROM view_participant;


--DROP VIEW view_participant