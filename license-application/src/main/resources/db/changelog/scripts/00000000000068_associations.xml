<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="addColumn_participant_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="participant" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="participant">
            <column name="dummy_column" type="varchar(255)" defaultValue="PARTICIPANT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_participant_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_participant" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_participant">
            <column name="dummy_column" type="varchar(255)" defaultValue="PARTICIPANT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_address_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="address" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="address">
            <column name="dummy_column" type="varchar(255)" defaultValue="ADDRESS">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_address_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_address" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_address">
            <column name="dummy_column" type="varchar(255)" defaultValue="ADDRESS">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_business_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="business" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="business">
            <column name="dummy_column" type="varchar(255)" defaultValue="BUSINESS">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_business_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_business" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_business">
            <column name="dummy_column" type="varchar(255)" defaultValue="BUSINESS">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_custom_entity_instance_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="custom_entity_instance" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="custom_entity_instance">
            <column name="dummy_column" type="varchar(255)" defaultValue="CUSTOM_ENTITY">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_custom_entity_instance_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_custom_entity_instance" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_custom_entity_instance">
            <column name="dummy_column" type="varchar(255)" defaultValue="CUSTOM_ENTITY">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_document_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="document" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="document">
            <column name="dummy_column" type="varchar(255)" defaultValue="DOCUMENT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_document_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_document" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_document">
            <column name="dummy_column" type="varchar(255)" defaultValue="DOCUMENT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_entity_fee_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="entity_fee" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="entity_fee">
            <column name="dummy_column" type="varchar(255)" defaultValue="ENTITY_FEE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_entity_fee_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_entity_fee" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_entity_fee">
            <column name="dummy_column" type="varchar(255)" defaultValue="ENTITY_FEE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_entity_group_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="entity_group" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="entity_group">
            <column name="dummy_column" type="varchar(255)" defaultValue="ENTITY_GROUP">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_entity_group_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_entity_group" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_entity_group">
            <column name="dummy_column" type="varchar(255)" defaultValue="ENTITY_GROUP">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_entity_note_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="entity_note" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="entity_note">
            <column name="dummy_column" type="varchar(255)" defaultValue="ENTITY_NOTE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_entity_note_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_entity_note" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_entity_note">
            <column name="dummy_column" type="varchar(255)" defaultValue="ENTITY_NOTE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_license_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="license" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="license">
            <column name="dummy_column" type="varchar(255)" defaultValue="LICENSE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_license_dummy_column" author="David">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_license" columnName="dummy_column"/>
            </not>
        </preConditions>
        <addColumn tableName="audit_log_license">
            <column name="dummy_column" type="varchar(255)" defaultValue="LICENSE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>