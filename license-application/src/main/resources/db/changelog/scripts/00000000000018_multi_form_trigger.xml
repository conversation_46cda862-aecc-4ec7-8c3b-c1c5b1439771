<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1695837524348-123">
        <createTable tableName="multi_form_trigger">
            <column autoIncrement="true" name="multi_form_trigger_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="multi_form_trigger_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="field_name" type="VARCHAR(100)"/>
            <column name="multi_form_form_element_id" type="BIGINT"/>
            <column name="multi_form_page_id" type="BIGINT"/>
            <column name="multi_form_section_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-124">
        <createTable tableName="audit_log_multi_form_trigger">
            <column name="multi_form_trigger_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_multi_form_trigger_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_multi_form_trigger_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="field_name" type="VARCHAR(100)"/>
            <column name="multi_form_form_element_id" type="BIGINT"/>
            <column name="multi_form_page_id" type="BIGINT"/>
            <column name="multi_form_section_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-125">
        <createTable tableName="audit_log_multi_form_trigger_field_values">
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="multi_form_trigger_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="field_values" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-126">
        <createTable tableName="multi_form_trigger_field_values">
            <column name="multi_form_trigger_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="field_values" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-127">
        <addPrimaryKey columnNames="multi_form_trigger_id, field_values, revision_id"
                       constraintName="audit_log_multi_form_trigger_field_values_pkey"
                       tableName="audit_log_multi_form_trigger_field_values"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-129">
        <addForeignKeyConstraint baseColumnNames="multi_form_trigger_id" baseTableName="multi_form_trigger_field_values"
                                 constraintName="fk1gyi5fa971ab2vh1dqmmrrcsf" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="multi_form_trigger_id" referencedTableName="multi_form_trigger"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-130">
        <addForeignKeyConstraint baseColumnNames="multi_form_form_element_id" baseTableName="multi_form_trigger"
                                 constraintName="fk2cfg08dyn64fw3gbwweuoy2sx" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="multi_form_form_element_id"
                                 referencedTableName="multi_form_form_element" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-131">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_multi_form_trigger"
                                 constraintName="fkc9d1hlpo8175own5eb4l2ykk3" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-132">
        <addForeignKeyConstraint baseColumnNames="multi_form_section_id" baseTableName="multi_form_trigger"
                                 constraintName="fkclu1nxbu2fea1ily3b07jtan6" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="multi_form_section_id" referencedTableName="multi_form_section"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-133">
        <addForeignKeyConstraint baseColumnNames="multi_form_page_id" baseTableName="multi_form_trigger"
                                 constraintName="fkclxp7rjoib69qfporl5dkhl5l" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="multi_form_page_id" referencedTableName="multi_form_page"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695837524348-134">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_multi_form_trigger_field_values"
                                 constraintName="fkrxdvmp2a267tnbv87mgqyxjkf" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
</databaseChangeLog>
