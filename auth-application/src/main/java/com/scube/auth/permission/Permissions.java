package com.scube.auth.permission;

/**
 * Generated DO NOT MODIFY!
 * date: 2024-03-15T17:03:45.660259700Z
 */
public class Permissions {
    private Permissions() {
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-03-15T17:03:45.660259700Z
     */
    public static class User {
        public static final String GET_ALL_USERS = "auth-service-user-get-all-users";

        public static final String GET_USER = "auth-service-user-get-user";
        public static final String DELETE_USER = "auth-service-user-delete-user";

        private User() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-03-15T17:03:45.667922600Z
     */
    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS = "auth-service-permissions-seed-roles-to-all-realms";

        public static final String SEED_ROLES_BY_REALM = "auth-service-permissions-seed-roles-by-realm";

        private Permission() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-03-15T17:03:45.668924500Z
     */
    public static class PublicRealm {
        public static final String GET_ALL_REALMS = "auth-service-public-realm-get-all-realms";

        public static final String GET_ALL_REALM_NAMES = "auth-service-public-realm-get-all-realm-names";

        public static final String GET_REALM = "auth-service-public-realm-get-realm";

        public static final String GET_REALM_NAME = "auth-service-public-realm-get-realm-name";

        private PublicRealm() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-03-15T17:03:45.668924500Z
     */
    public static class LoggedInUser {
        public static final String GET_USER_PROFILE = "auth-service-me-user-get-user-profile";

        public static final String GET_USER = "auth-service-me-user-get-user";
        public static final String DELETE_USER = "auth-service-me-user-delete-user";

        private LoggedInUser() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-03-15T17:03:45.668924500Z
     */
    public static class Realm {
        public static final String GET_REALM_REPRESENTATION = "auth-service-realm-get-realm-representation";

        public static final String CREATE_REALM = "auth-service-realm-create-realm";

        public static final String ADD_ROLE_TO_REALM = "auth-service-realm-add-role-to-realm";

        public static final String CREATE_COMPOSITE_ROLE = "auth-service-realm-create-composite-role";

        private Realm() {
        }
    }
}
