package com.scube.auth;

import com.scube.rabbit.core.annotation.EnableRabbitMQLibrary;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@EnableRabbitMQLibrary(additionalPackages = "com.scube")
@OpenAPIDefinition(info = @Info(title = "Auth Service API", version = "1.0", description = "Swagger Documentation"))
public class AuthServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthServiceApplication.class, args);
    }
}