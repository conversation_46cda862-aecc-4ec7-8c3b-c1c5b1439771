package com.scube.notification.features.status.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.notification.features.permission.Permissions;
import com.scube.notification.features.status.dto.NotificationStatusCreateRequest;
import com.scube.notification.features.status.dto.NotificationStatusUpdateRequest;
import com.scube.notification.features.status.service.NotificationStatusService;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/status")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.NOTIFICATION_SERVICE)
public class NotificationStatusController {
    private final NotificationStatusService notificationStatusService;

    @GetMapping
    @RolesAllowed(Permissions.NotificationStatus.GET_ALL)
    public ResponseEntity<List<?>> getAll() {
        log.debug("NotificationStatusController.getAll()");
        return ResponseEntity.ok(notificationStatusService.findAll());
    }

    @GetMapping("/{uuid}")
    @RolesAllowed(Permissions.NotificationStatus.GET)
    public ResponseEntity<?> get(@PathVariable UUID uuid) {
        log.debug("NotificationStatusController.get()");
        return ResponseEntity.ok(notificationStatusService.findByUuid(uuid));
    }

    @PostMapping
    @RolesAllowed(Permissions.NotificationStatus.CREATE)
    public ResponseEntity<?> create(@RequestBody NotificationStatusCreateRequest request) {
        log.debug("NotificationStatusController.create()");
        return ResponseEntity.status(HttpStatus.CREATED).body(notificationStatusService.create(request));
    }

    @PutMapping
    @RolesAllowed(Permissions.NotificationStatus.UPDATE)
    public ResponseEntity<?> update(@RequestBody NotificationStatusUpdateRequest request) {
        log.debug("NotificationStatusController.update()");
        return ResponseEntity.ok(notificationStatusService.update(request));
    }

    @DeleteMapping("/{uuid}")
    @RolesAllowed(Permissions.NotificationStatus.DELETE)
    public ResponseEntity<?> delete(@PathVariable UUID uuid) {
        log.debug("NotificationStatusController.delete()");
        notificationStatusService.deleteByUuid(uuid);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
