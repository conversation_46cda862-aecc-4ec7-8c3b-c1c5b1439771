package com.scube.notification.features.notification.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.notification.db.entity.NotificationType;
import com.scube.notification.features.notification.validation.annotation.ValidNotificationCreateRequest;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ValidNotificationCreateRequest
@SuperBuilder
public class NotificationCreateRequest extends NotificationMetadata {
	@NotNull
	private NotificationType notificationType;
	@NotNull
	private JsonNode notification;
}
