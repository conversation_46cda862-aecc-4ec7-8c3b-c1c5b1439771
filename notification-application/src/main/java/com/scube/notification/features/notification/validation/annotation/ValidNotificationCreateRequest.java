package com.scube.notification.features.notification.validation.annotation;

import com.scube.notification.features.notification.validation.validator.NotfificationCreateRequestValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Documented
@Target(TYPE)
@Retention(RUNTIME)
@Constraint(validatedBy = { NotfificationCreateRequestValidator.class })
public @interface ValidNotificationCreateRequest {
    String message() default "{com.scube.notification.features.validation.annotation.ValidNotificationCreateRequest.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
