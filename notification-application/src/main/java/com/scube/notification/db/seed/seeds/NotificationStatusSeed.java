package com.scube.notification.db.seed.seeds;

import com.scube.notification.db.entity.NotificationStatus;
import com.scube.notification.db.repository.NotificationStatusRepository;
import com.scube.notification.db.seed.config.ISeed;
import com.scube.notification.db.seed.config.Seed;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.scube.notification.features.status.constants.StatusConstants.*;

@Component
@Slf4j
@AllArgsConstructor
@Seed(priority = 2)
public class NotificationStatusSeed implements ISeed {
    private final NotificationStatusRepository notificationStatusRepository;

    @Transactional
    public void seed() {
        List<String> statuses = List.of(PENDING, COMPLETE, FAILED, CANCELED);

        for (String status : statuses) {
            if (!notificationStatusRepository.existsByName(status)) {
                NotificationStatus notificationStatus = NotificationStatus.builder()
                        .name(status)
                        .createdBy(SYSTEM)
                        .lastModifiedBy(SYSTEM)
                        .build();

                notificationStatusRepository.save(notificationStatus);
            }
        }
    }
}
