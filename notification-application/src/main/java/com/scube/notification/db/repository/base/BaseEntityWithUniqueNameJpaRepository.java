package com.scube.notification.db.repository.base;

import com.scube.audit.auditable.entity.IAuditableEntity;
import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import jakarta.validation.constraints.Size;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.Optional;

/**
 * Base repository for entities that include a name field
 */
@NoRepositoryBean
public interface BaseEntityWithUniqueNameJpaRepository<T extends IAuditableEntity<ID>, ID extends Serializable> extends AuditableEntityRepository<T, ID> {
    boolean existsByName(@Size(max = 255) String name);

    Optional<T> findByName(@Size(max = 255) String name);
}
