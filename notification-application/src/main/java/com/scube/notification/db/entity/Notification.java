package com.scube.notification.db.entity;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.notification.db.entity.base.BaseEntity;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = Notification.TABLE_NAME)
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Audited
public class Notification extends BaseEntity {
    public static final String TABLE_NAME = "notification";
    public static final String ID = "notification_id";
    public static final String EXECUTION_TS = "execution_ts";
    public static final String NOTIFICATION_TYPE = "notification_type";
    public static final String CORRELATION_ID = "correlation_id";

    @Column(name = CORRELATION_ID, updatable = false)
    private UUID correlationId;

    @Size(max = 255)
    private String tag;

    @Size(max = 255)
    private String topic;

    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type")
    private NotificationType notificationType;

    @ManyToOne
    @JoinColumn(name = NotificationStatus.ID, nullable = false)
    private NotificationStatus notificationStatus;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private JsonNode notification;

    @Column(name = EXECUTION_TS)
    private LocalDateTime executionTs;

    @PrePersist
    @PreUpdate
    void prePersistNotification() {
        if (notification == null) {
            notification = new ObjectMapper().createObjectNode();
        }
    }
}
