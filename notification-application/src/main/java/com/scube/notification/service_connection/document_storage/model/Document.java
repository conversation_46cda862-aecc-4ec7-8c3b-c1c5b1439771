package com.scube.notification.service_connection.document_storage.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.util.UUID;

@Builder
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class Document {
    @JsonIgnore
    private Long id;
    private UUID documentUUID;
    private String documentUrl;
    private String contentType;
    private String name;
    private String originalName;
    private Long size;
}
