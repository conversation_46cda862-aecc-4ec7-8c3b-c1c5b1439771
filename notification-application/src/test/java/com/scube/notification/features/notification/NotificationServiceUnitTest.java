package com.scube.notification.features.notification;

import com.scube.notification.db.entity.Notification;
import com.scube.notification.db.projection.NotificationProjection;
import com.scube.notification.db.repository.NotificationRepository;
import com.scube.notification.exception.NotFoundException;
import com.scube.notification.features.notification.dto.NotificationCreateRequest;
import com.scube.notification.features.notification.service.NotificationService;
import com.scube.notification.features.status.service.NotificationStatusService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class NotificationServiceUnitTest {
    @InjectMocks
    @Spy
    private NotificationService notificationService;

    @Mock
    private NotificationRepository notificationRepository;

    @Mock
    private NotificationStatusService notificationStatusService;

    private UUID uuid;

    @BeforeEach
    public void setUp() {
        uuid = UUID.randomUUID();
    }

    @Test
    void findAll() {
        List<NotificationProjection> expectedNotifications = List.of(mock(NotificationProjection.class));

        when(notificationRepository.findAllBy()).thenReturn(expectedNotifications);

        assertEquals(expectedNotifications, notificationService.findAll());
    }

    @Test
    void findByUuid() {
        Notification expectedNotification = new Notification();

        when(notificationRepository.findByUuid(uuid)).thenReturn(Optional.of(expectedNotification));

        assertEquals(expectedNotification, notificationService.findByUuid(uuid));
    }

    @Test
    void findByUuid_NotFound() {
        when(notificationRepository.findByUuid(uuid)).thenReturn(Optional.empty());

        assertThrows(NotFoundException.class, () -> notificationService.findByUuid(uuid));
    }

    @Test
    void findProjectionByUuid() {
        NotificationProjection expectedProjection = mock(NotificationProjection.class);

        when(notificationRepository.findProjectionByUuid(uuid)).thenReturn(Optional.of(expectedProjection));

        assertEquals(expectedProjection, notificationService.findProjectionByUuid(uuid));
    }

    @Test
    void findProjectionByUuid_NotFound() {
        when(notificationRepository.findProjectionByUuid(uuid)).thenReturn(Optional.empty());

        assertThrows(NotFoundException.class, () -> notificationService.findProjectionByUuid(uuid));
    }

    @Test
    void create() {
        Notification expectedNotification = new Notification();
        expectedNotification.setUuid(uuid);

        NotificationProjection expectedProjection = mock(NotificationProjection.class);

        NotificationCreateRequest request = new NotificationCreateRequest();

        when(notificationRepository.save(any())).thenReturn(expectedNotification);
        doReturn(expectedProjection).when(notificationService).findProjectionByUuid(uuid);

        assertEquals(expectedProjection, notificationService.create(request));
    }

    @Test
    void save() {
        Notification expectedNotification = new Notification();

        when(notificationRepository.save(any())).thenReturn(expectedNotification);

        assertEquals(expectedNotification, notificationService.save(expectedNotification));
    }

    @Test
    void deleteByUuid() {
        Notification existingNotification = new Notification();

        doReturn(existingNotification).when(notificationService).findByUuid(uuid);
        doNothing().when(notificationRepository).delete(existingNotification);

        notificationService.deleteByUuid(uuid);

        verify(notificationRepository, times(1)).delete(existingNotification);
    }

    @Test
    void findPendingNotificationsReadyToSend() {
        List<Notification> expectedNotifications = List.of(new Notification());

        when(notificationService.findPendingNotificationsReadyToSend()).thenReturn(expectedNotifications);

        assertEquals(expectedNotifications, notificationService.findPendingNotificationsReadyToSend());
    }
}
