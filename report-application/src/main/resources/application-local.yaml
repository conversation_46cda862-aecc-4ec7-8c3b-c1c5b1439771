server:
  port: 9007
  servlet:
    context-path: /api/report
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: always
    include-exception: true
    include-client-error-message: true
    include-server-error-message: true
    send-client-error-email: false
    send-server-error-email: false
  forward-headers-strategy: framework

logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    org.springframework.web: "info"
    org.hibernate: "error"
    liquibase: "info"
    com.scube: "debug"

spring:
  application:
    name: ReportService
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB

  jpa:
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
    properties:
      hibernate:
        format_sql: true


springdoc:
  show-actuator: true
  swagger-ui:
    filter: true

keycloak:
  host: ${KEYCLOAK_URL:http://localhost:8080}
  public-host: ${KEYCLOAK_PUBLIC_URL:http://localhost:3030}
  admin:
    url: ${keycloak.host}
    realm: master
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID}
    client-secret: ${KEYCLOAK_ADMIN_CLIENT_SECRET}
  swagger:
    url: ${keycloak.host}
    realm: clerkXpress
    client-id: ${KEYCLOAK_SWAGGER_AUTH_CLIENT_ID}

com.c4-soft.springaddons.oidc:
  ops:
    - iss: ${keycloak.public-host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - http://localhost:3000
          - https://localhost:3000
          - ${keycloak.public-host}

scheduling:
  enabled: true

multi-tenancy:
  enabled: true
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: ${SPRING_RABBITMQ_HOST:localhost}
    port: 5672
  scheduling:
    enabled: true
  database:
    enabled: true
    type: DATABASE_PER_TENANT
    default-tenant: postgres
    tenancy-format: "%s"
    liquibase:
      change-log: "classpath:/db/changelog/db.changelog-master.xml"
    datasource:
      url: "jdbc:postgresql://${DATABASE_HOST:localhost}:5432/postgres?currentSchema=report"
      username: ${SPRING_DATASOURCE_USERNAME}
      password: ${SPRING_DATASOURCE_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximumPoolSize: 5
        minimumIdle: 5
        idleTimeout: 30000
        maxLifetime: 60000
        connectionTimeout: 30000

com.scube.client:
  ai: "http://${CLIENT_LIB_HOST:localhost}:9011/api/ai"
  auth: "http://${CLIENT_LIB_HOST:localhost}:9001/api/auth"
  calculation: "http://${CLIENT_LIB_HOST:localhost}:9002/api/calculation"
  document: "http://${CLIENT_LIB_HOST:localhost}:9003/api/document-service"
  documentTemplateHelper: "http://${CLIENT_LIB_HOST:localhost}:9012/api/document-template-helper"
  documentTemplate: "http://${CLIENT_LIB_HOST:localhost}:9009/api/document-template"
  imageProcessing: "http://${CLIENT_LIB_HOST:localhost}:9010/api/image-processing"
  license: "http://${CLIENT_LIB_HOST:localhost}:9004/api/license"
  notification: "http://${CLIENT_LIB_HOST:localhost}:9005/api/notification"
  ocr: "http://${CLIENT_LIB_HOST:localhost}:9008/api/ocr"
  payment: "http://${CLIENT_LIB_HOST:localhost}:9006/api/payment"
  report: "http://${CLIENT_LIB_HOST:localhost}:9007/api/report"
