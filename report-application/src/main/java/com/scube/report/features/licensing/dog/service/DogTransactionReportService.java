package com.scube.report.features.licensing.dog.service;

import com.scube.auth.library.ITokenService;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.payment.generated.PaymentServiceConnection;
import com.scube.payment.features.payment.processing.dto.gen_dto.GetPaymentResponseDto;
import com.scube.report.features.base.service.IReportQueryService;
import com.scube.report.features.base.util.RabbitMqUtil;
import com.scube.report.features.licensing.dog.PaymentTotals;
import com.scube.report.features.licensing.dog.dto.LicenseProjection;
import com.scube.report.features.licensing.dog.dto.ReportHeader;
import com.scube.report.features.licensing.dog.dto.reports.DogTransactionReport;
import com.scube.report.features.licensing.dog.dto.request.ReportRequest;
import com.scube.report.features.licensing.dog.enums.PaymentType;
import com.scube.report.features.licensing.dog.rabbit.GetLicenseActivitiesByOrderIdQuery;
import com.scube.report.features.licensing.dog.rabbit.GetLicenseProjectionQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.scube.report.features.base.util.DateUtil.USA_DATE_FORMAT;
import static com.scube.report.features.base.util.DateUtil.parseInstant;
import static com.scube.report.features.base.util.ValidationUtil.validate;
import static com.scube.report.features.licensing.dog.util.DogLicenseReportUtil.*;
import static org.apache.commons.lang3.StringUtils.defaultString;

@Service("DogTransactionReport")
@Slf4j
@RequiredArgsConstructor
public class DogTransactionReportService implements IReportQueryService {
    private static final String REPORT_NAME = "DogTransactionReport";
    private final CalculationServiceWrapper calculationService;
    private final PaymentServiceConnection paymentService;
    private final ITokenService tokenService;

    public Object execute(Map<String, Object> params) {
        log.debug("DogLicenseReportService.getReport()");

        ReportRequest request = new ReportRequest(params);
        validate(request);

        var licenses = new ArrayList<DogTransactionReport.License>();

        PaymentTotals paymentTotals = new PaymentTotals();

        List<OrderInvoiceResponse> regularDogLicenseOrders = calculationService.getRegularDogOrders(params);

        List<OrderInvoiceResponse> purebredOrders = calculationService.getPurebredDogOrders(params);

        countRegularDogLicensePayments(regularDogLicenseOrders, licenses, paymentTotals);
        countPurebredPayments(purebredOrders, licenses, paymentTotals);

        DogTransactionReport dogTransactionReport = new DogTransactionReport();
        dogTransactionReport.setReportHeader(ReportHeader.of(params, tokenService.getLoggedInUserInfo()));
        dogTransactionReport.setLicenses(licenses);
        dogTransactionReport.setCashTotal(paymentTotals.getCashTotal());
        dogTransactionReport.setCreditTotal(paymentTotals.getCreditTotal());
        dogTransactionReport.setPersonalCheckTotal(paymentTotals.getPersonalCheckTotal());
        dogTransactionReport.setMoneyOrderTotal(paymentTotals.getMoneyOrderTotal());
        dogTransactionReport.setCertifiedCheckTotal(paymentTotals.getCertifiedCheckTotal());
        dogTransactionReport.setAchTotal(paymentTotals.getAchTotal());

        return dogTransactionReport;
    }

    private void countRegularDogLicensePayments(List<OrderInvoiceResponse> regularDogLicenseOrders, ArrayList<DogTransactionReport.License> licenses, PaymentTotals paymentTotals) {
        //Loop through the orders to get the payment amount and payment type
        for (OrderInvoiceResponse order : regularDogLicenseOrders) {
            //Get the payment type for the order from payment service
            List<GetPaymentResponseDto> payments = paymentService.payment().getPaymentsByOrderId(order.getOrderId());
            var payment = new GetPaymentResponseDto();
            if (payments == null || payments.size() == 0) {
                throw new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Failed to retrieve payment information for order: " + order.getOrderId().toString());
            } else {
                payment = payments.get(0);
            }

            PaymentType paymentType = PaymentType.from(payment.getPaymentType());

            //Loop through the order items and get the license and payment information
            for (var item : order.getItems()) {
                if (item == null) {
                    continue;
                }

                //Retrieve the dog license information from license service
                LicenseProjection dogLicense = RabbitMqUtil.query(new GetLicenseProjectionQuery(item.getItemId().toString()));
                List<String> licenseActivities = RabbitMqUtil.query(new GetLicenseActivitiesByOrderIdQuery(order.getOrderId().toString())).licenseActivities();

                //If the license is not null then add it to the list of licenses
                if (dogLicense != null) {
                    licenses.add(DogTransactionReport.License.builder()
                            .date(order.getOrderPaidDate() == null ? "" : parseInstant(order.getOrderPaidDate(), USA_DATE_FORMAT))
                            .licenseNumber(dogLicense.getLicenseNumber())
                            .ownerName(defaultString(dogLicense.getOwnerFirstName()) + " " + defaultString(dogLicense.getOwnerLastName()))
                            .feeName(getLicenseTypeForDogTransactionReport(item, dogLicense, licenseActivities))
                            .feeDescription(getLicenseDescription(item))
                            .expirationDate(dogLicense.getValidToDate().format(DateTimeFormatter.ofPattern(USA_DATE_FORMAT)))
                            .localFee(getLocalFees(item))
                            .stateFee(getStateFees(item))
                            .totalFee(getTotalFees(item))
                            .build());
                }

                switch (paymentType) {
                    case CASH -> paymentTotals.addCash(item.getTotal());
                    case PERSONAL_CHECK -> paymentTotals.addPersonalCheck(item.getTotal());
                    case CERTIFIED_CHECK -> paymentTotals.addCertifiedCheck(item.getTotal());
                    case CREDIT -> paymentTotals.addCredit(item.getTotal());
                    case MONEY_ORDER -> paymentTotals.addMoneyOrder(item.getTotal());
                    case ACH -> paymentTotals.addAch(item.getTotal());
                    default -> paymentTotals.addUnknown(item.getTotal());
                }
            }
        }
    }

    private void countPurebredPayments(List<OrderInvoiceResponse> purebredLicenseOrders, ArrayList<DogTransactionReport.License> licenses, PaymentTotals paymentTotals) {
        //Loop through the orders to get the payment amount and payment type
        for (OrderInvoiceResponse order : purebredLicenseOrders) {
            //Get the payment type for the order from payment service
            List<GetPaymentResponseDto> payments = paymentService.payment().getPaymentsByOrderId(order.getOrderId());
            var payment = new GetPaymentResponseDto();
            if (payments == null || payments.size() == 0) {
                throw new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Failed to retrieve payment information for order: " + order.getOrderId().toString());
            } else {
                payment = payments.get(0);
            }

            PaymentType paymentType = PaymentType.from(payment.getPaymentType());

            //Loop through the order items and get the license and payment information
            for (var item : order.getItems()) {
                if (item == null) {
                    continue;
                }

                //Retrieve the dog license information from license service
                LicenseProjection dogLicense = RabbitMqUtil.query(new GetLicenseProjectionQuery(item.getItemId().toString()));
                List<String> licenseActivities = RabbitMqUtil.query(new GetLicenseActivitiesByOrderIdQuery(order.getOrderId().toString())).licenseActivities();

                //If the license is not null then add it to the list of licenses
                if (dogLicense != null) {
                    licenses.add(DogTransactionReport.License.builder()
                            .date(order.getOrderPaidDate() == null ? "" : parseInstant(order.getOrderPaidDate(), USA_DATE_FORMAT))
                            .licenseNumber(dogLicense.getLicenseNumber())
                            .ownerName(defaultString(dogLicense.getOwnerFirstName()) + " " + defaultString(dogLicense.getOwnerLastName()))
                            .feeName(getLicenseTypeForPurebredDogTransactionReport(item, dogLicense, licenseActivities))
                            .feeDescription(getLicenseDescription(item))
                            .expirationDate(dogLicense.getValidToDate().format(DateTimeFormatter.ofPattern(USA_DATE_FORMAT)))
                            .localFee(getLocalFees(item))
                            .stateFee(getStateFees(item))
                            .totalFee(getTotalFees(item))
                            .build());
                }

                switch (paymentType) {
                    case CASH:
                        paymentTotals.addCash(item.getTotal());
                        break;
                    case PERSONAL_CHECK:
                        paymentTotals.addPersonalCheck(item.getTotal());
                        break;
                    case CERTIFIED_CHECK:
                        paymentTotals.addCertifiedCheck(item.getTotal());
                        break;
                    case CREDIT:
                        paymentTotals.addCredit(item.getTotal());
                        break;
                    case MONEY_ORDER:
                        paymentTotals.addMoneyOrder(item.getTotal());
                        break;
                    case ACH:
                        paymentTotals.addAch(item.getTotal());
                        break;
                    default:
                        throw new IllegalArgumentException("Unknown payment type: " + paymentType.getKey());
                }
            }
        }
    }
}
