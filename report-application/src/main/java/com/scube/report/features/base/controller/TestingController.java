package com.scube.report.features.base.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.report.features.base.dto.GenerateReportRequest;
import com.scube.report.features.base.service.ReportService;
import com.scube.report.features.base.service.TemplateService;
import com.scube.report.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/test")
@Slf4j
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.REPORT_SERVICE)
@Validated
public class TestingController {
    private final TemplateService testingService;
    private final ReportService reportService;
    private final ObjectMapper objectMapper;

    @GetMapping("templates")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Testing.GET_TEMPLATES)
    public List getTemplates() {
        return testingService.getTemplates();
    }

    @GetMapping("template/{templateKey}")
    @RolesAllowed(Permissions.Testing.DOWNLOAD_TEMPLATE)
    public ResponseEntity<Resource> downloadTemplate(@PathVariable @NotEmpty String templateKey) {
        return testingService.downloadTemplate(templateKey);
    }

    @PostMapping("/generate/pdf/{templateKey}")
    @RolesAllowed(Permissions.Testing.GENERATE_PDF_REPORT_BY_TEMPLATE_KEY)
    public ResponseEntity<Resource> generatePdfReportByTemplateKey(@PathVariable @NotEmpty String templateKey,
                                          @RequestBody GenerateReportRequest request) {
        return reportService.generatePdfReport(templateKey, request);
    }

    @PostMapping(value = "generate/pdf", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @RolesAllowed(Permissions.Testing.GENERATE_PDF_REPORT)
    @SneakyThrows
    public ResponseEntity<Resource> generatePdfReport(@RequestParam("template") MultipartFile template,
                                             @RequestParam("reportRequest") @NotEmpty String request) {

        GenerateReportRequest generateReportRequest = objectMapper.readValue(request, GenerateReportRequest.class);

        return reportService.generatePdfReport(template, generateReportRequest);
    }
}