package com.scube.report.features.licensing.dog.dto.reports;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.scube.report.features.licensing.dog.dto.ReportHeader;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClerksMonthlyReport {
    @JsonUnwrapped
    private ReportHeader reportHeader;

    @JsonProperty("localItems")
    private List<LocalItem> localItems;

    @JsonProperty("localSubotal")
    private int localSubtotal;

    @JsonProperty("nonLocalSubotal")
    private int nonLocalSubtotal;

    @JsonProperty("total")
    private int total;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LocalItem {
        @JsonProperty("accountDescription")
        private String accountDescription;

        @JsonProperty("feeDescription")
        private String feeDescription;

        @JsonProperty("quantity")
        private String quantity;

        @JsonProperty("fee")
        private String fee;

        @JsonProperty("localShare")
        private String localShare;
    }
}
