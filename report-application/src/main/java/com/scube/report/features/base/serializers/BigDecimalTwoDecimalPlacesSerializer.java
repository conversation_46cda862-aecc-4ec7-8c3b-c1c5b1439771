package com.scube.report.features.base.serializers;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

public class BigDecimalTwoDecimalPlacesSerializer extends JsonSerializer<BigDecimal> {

    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // Ensure that the BigDecimal has exactly two decimal places

        BigDecimal roundedValue = value == null ? new BigDecimal("0.00") : value;
        gen.writeString(roundedValue.setScale(2, BigDecimal.ROUND_CEILING).toString());
    }
}

