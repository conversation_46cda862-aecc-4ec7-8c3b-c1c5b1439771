package com.scube.report.features.licensing.dog.service;

import com.scube.auth.library.ITokenService;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.report.features.base.service.IReportQueryService;
import com.scube.report.features.base.util.RabbitMqUtil;
import com.scube.report.features.licensing.dog.dto.LicenseProjection;
import com.scube.report.features.licensing.dog.dto.ReportHeader;
import com.scube.report.features.licensing.dog.dto.reports.ClerkSummaryReport;
import com.scube.report.features.licensing.dog.dto.request.ReportRequest;
import com.scube.report.features.licensing.dog.enums.ActivityType;
import com.scube.report.features.licensing.dog.rabbit.GetLicenseActivitiesByOrderIdQuery;
import com.scube.report.features.licensing.dog.rabbit.GetLicenseProjectionQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.scube.report.features.base.util.ValidationUtil.validate;
import static com.scube.report.features.licensing.dog.constants.ReportConstants.*;
import static com.scube.report.features.licensing.dog.util.DogLicenseReportUtil.*;


@Service("ClerkSummaryReport")
@Slf4j
@RequiredArgsConstructor
public class ClerkSummaryReportService implements IReportQueryService {
    private static final String REPORT_NAME = "ClerkSummaryReport";
    private final CalculationServiceWrapper calculationService;
    private final ITokenService tokenService;

    private static final String NEW_ALTERED = "New, Altered";
    private static final String NEW_UNALTERED = "New, Unaltered";
    private static final String NEW_ALTERED_SENIOR = "New, Altered - Senior";
    private static final String NEW_UNALTERED_SENIOR = "New, Unaltered - Senior";
    private static final String RENEWAL_ALTERED = "Renewal, Altered";
    private static final String RENEWAL_UNALTERED = "Renewal, Unaltered";
    private static final String RENEWAL_ALTERED_SENIOR = "Renewal, Altered - Senior";
    private static final String RENEWAL_UNALTERED_SENIOR = "Renewal, Unaltered - Senior";
    private static final String LICENSE_EXEMPT = "License Exempt";
    private static final String PUREBRED_BASE_UNALTERED = "Purebred Base - Unaltered";
    private static final String PUREBRED_BASE_ALTERED = "Purebred Base - Altered";
    private static final String PUREBRED_ADDON_UNALTERED = "Purebred Addon - Unaltered";
    private static final String PUREBRED_ADDON_ALTERED = "Purebred Addon - Altered";

    @Override
    public Object execute(Map<String, Object> params) {
        log.debug("ReportService.getAnimalPopulationControlFundData()");

        ReportRequest request = new ReportRequest(params);
        validate(request);

        ClerkSummaryReport clerkSummaryReport = new ClerkSummaryReport();
        clerkSummaryReport.setReportHeader(ReportHeader.of(params, tokenService.getLoggedInUserInfo()));

        List<ClerkSummaryReport.TableItem> tableItems = getLicenseTableItems(params);
        tableItems.add(getDogTagTableItem(params));

        tableItems.sort(ClerkSummaryReport.TableItem.ORDER_COMPARATOR);

        clerkSummaryReport.setTableItems(tableItems);

        return clerkSummaryReport;
    }

    private ClerkSummaryReport.TableItem getDogTagTableItem(Map<String, Object> params) {
        List<OrderInvoiceResponse> dogTagOrders = calculationService.getDogTagOrders(params);

        BigDecimal dogTagAmount = new BigDecimal("3.00");
        Integer dogTagQuantity = 0;

        //Loop through the dog tag orders and count the dog tags
        for (OrderInvoiceResponse order : dogTagOrders) {
            for (var item : order.getItems()) {
                if (item == null) {
                    continue;
                }
                if (("Dog Tag").equals(item.getPrimaryDisplay())) {
                    dogTagQuantity += 1;
                }
            }
        }

        return ClerkSummaryReport.TableItem.builder()
                .title("Dog Tag")
                .amount(dogTagAmount)
                .quantity(dogTagQuantity)
                .order(14)
                .build();
    }

    private static List<ClerkSummaryReport.TableItem> getLicenseTableItems() {
        List<ClerkSummaryReport.TableItem> tableItems = new ArrayList<>();

        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(NEW_ALTERED)
                .amount(ALTERED_FEE)
                .order(1)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(NEW_UNALTERED)
                .amount(UNALTERED_FEE)
                .order(2)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(NEW_ALTERED_SENIOR)
                .amount(ALTERED_W_SENIOR_DISCOUNT)
                .order(3)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(NEW_UNALTERED_SENIOR)
                .amount(UNALTERED_W_SENIOR_DISCOUNT)
                .order(4)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(RENEWAL_ALTERED)
                .amount(ALTERED_FEE)
                .order(5)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(RENEWAL_UNALTERED)
                .amount(UNALTERED_FEE)
                .order(6)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(RENEWAL_ALTERED_SENIOR)
                .amount(ALTERED_W_SENIOR_DISCOUNT)
                .order(7)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(RENEWAL_UNALTERED_SENIOR)
                .amount(UNALTERED_W_SENIOR_DISCOUNT)
                .order(8)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(LICENSE_EXEMPT)
                .amount(EXEMPT_AMOUNT)
                .order(9)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(PUREBRED_BASE_ALTERED)
                .amount(PUREBRED_BASE_ALTERED_FEE)
                .order(10)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(PUREBRED_BASE_UNALTERED)
                .amount(PUREBRED_BASE_UNALTERED_FEE)
                .order(11)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(PUREBRED_ADDON_ALTERED)
                .amount(PUREBRED_ADDON_ALTERED_FEE)
                .order(12)
                .build());
        tableItems.add(ClerkSummaryReport.TableItem.builder()
                .title(PUREBRED_ADDON_UNALTERED)
                .amount(PUREBRED_ADDON_UNALTERED_FEE)
                .order(13)
                .build());

        return tableItems;
    }

    private HashMap<String, ClerkSummaryReport.TableItem> getLicenseQuantityHashMap(List<ClerkSummaryReport.TableItem> tableItems) {
        HashMap<String, ClerkSummaryReport.TableItem> tableItemMap = new HashMap<>();

        for (ClerkSummaryReport.TableItem item : tableItems) {
            tableItemMap.put(item.getTitle(), item);
        }

        return tableItemMap;
    }

    private List<ClerkSummaryReport.TableItem> getLicenseTableItems(Map<String, Object> params) {
        var licenseQuantityHashMap = getLicenseQuantityHashMap(getLicenseTableItems());

        List<OrderInvoiceResponse> dogLicenseOrders = calculationService.getRegularDogOrders(params);
        List<OrderInvoiceResponse> purebredOrders = calculationService.getPurebredDogOrders(params);

        countPurebredLicenses(purebredOrders, licenseQuantityHashMap);
        countDogLicenses(dogLicenseOrders, licenseQuantityHashMap);

        return new ArrayList<>(licenseQuantityHashMap.values());
    }

    private void countPurebredLicenses(List<OrderInvoiceResponse> purebredOrders, HashMap<String, ClerkSummaryReport.TableItem> licenseQuantityHashMap) {
        for (OrderInvoiceResponse order : purebredOrders) {
            //Loop through the order items and get the license and payment information
            for (var item : order.getItems()) {
                if (item == null) {
                    continue;
                }

                //Need to call license service to determine if its a new or renewal
                LicenseProjection dogLicense = RabbitMqUtil.query(new GetLicenseProjectionQuery(item.getItemId().toString()));
                List<String> licenseActivities = RabbitMqUtil.query(new GetLicenseActivitiesByOrderIdQuery(order.getOrderId().toString())).licenseActivities();

                if (dogLicense == null) {
                    continue;
                }
                if (licenseActivities == null) {
                    continue;
                }

                for (String activityStr : licenseActivities) {
                    ActivityType activity = ActivityType.fromString(activityStr);

                    switch (activity) {
                        case NEW, RENEWAL -> {
                            if (isAltered(item)) {
                                licenseQuantityHashMap.get(PUREBRED_BASE_ALTERED).incrementQuantity();
                            } else {
                                licenseQuantityHashMap.get(PUREBRED_BASE_UNALTERED).incrementQuantity();
                            }
                        }
                        case ADD_PUREBRED_DOG -> {
                            if (isAltered(item)) {
                                licenseQuantityHashMap.get(PUREBRED_ADDON_ALTERED).incrementQuantity();
                            } else {
                                licenseQuantityHashMap.get(PUREBRED_ADDON_UNALTERED).incrementQuantity();
                            }
                        }
                        case OTHER -> {
                            log.error("ClerkSummaryReportService: Unknown activity type: " + activityStr);
                        }
                    }
                }
            }
        }
    }

    private void countDogLicenses(List<OrderInvoiceResponse> dogLicenseOrders, HashMap<String, ClerkSummaryReport.TableItem> licenseQuantityHashMap) {
        for (OrderInvoiceResponse order : dogLicenseOrders) {
            //Loop through the order items and get the license and payment information
            for (var item : order.getItems()) {
                if (item == null) {
                    continue;
                }

                //For exempt licenses we can skip all the other conditions
                if (isExempt(item)) {
                    licenseQuantityHashMap.get(LICENSE_EXEMPT).incrementQuantity();
                    continue;
                }
                //Need to call license service to determine if its a new or renewal
                LicenseProjection dogLicense = RabbitMqUtil.query(new GetLicenseProjectionQuery(item.getItemId().toString()));
                List<String> licenseActivities = RabbitMqUtil.query(new GetLicenseActivitiesByOrderIdQuery(order.getOrderId().toString())).licenseActivities();

                if (dogLicense == null) {
                    continue;
                }
                if (licenseActivities == null) {
                    continue;
                }

                for (String activityStr : licenseActivities) {
                    ActivityType activity = ActivityType.fromString(activityStr);

                    switch (activity) {
                        case NEW -> {
                            if (isAltered(item)) {
                                if (hasSeniorDiscount(item)) {
                                    licenseQuantityHashMap.get(NEW_ALTERED_SENIOR).incrementQuantity();
                                } else {
                                    licenseQuantityHashMap.get(NEW_ALTERED).incrementQuantity();
                                }
                            } else {
                                if (hasSeniorDiscount(item)) {
                                    licenseQuantityHashMap.get(NEW_UNALTERED_SENIOR).incrementQuantity();
                                } else {
                                    licenseQuantityHashMap.get(NEW_UNALTERED).incrementQuantity();
                                }
                            }
                        }
                        case RENEWAL -> {
                            if (isAltered(item)) {
                                if (hasSeniorDiscount(item)) {
                                    licenseQuantityHashMap.get(RENEWAL_ALTERED_SENIOR).incrementQuantity();
                                } else {
                                    licenseQuantityHashMap.get(RENEWAL_ALTERED).incrementQuantity();
                                }
                            } else {
                                if (hasSeniorDiscount(item)) {
                                    licenseQuantityHashMap.get(RENEWAL_UNALTERED_SENIOR).incrementQuantity();
                                } else {
                                    licenseQuantityHashMap.get(RENEWAL_UNALTERED).incrementQuantity();
                                }
                            }
                        }
                        case OTHER -> {
                            log.error("ClerkSummaryReportService: Unknown activity type: " + activityStr);
                        }
                    }
                }
            }
        }
    }
}
