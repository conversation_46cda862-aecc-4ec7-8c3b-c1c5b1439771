package com.scube.report.features.base.repository;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.report.features.base.entity.FormSection;
import jakarta.validation.constraints.Size;

import java.util.Optional;

public interface FormSectionRepository extends AuditableEntityRepository<FormSection, Long> {
    Optional<FormSection> findByName(@Size(max = 255) String name);
}