package com.scube.payment.features.permission;

/**
 * Generated DO NOT MODIFY!
 * date: 2025-04-23T15:01:08.103022900Z
 */
public class Permissions {
    private Permissions() {
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-04-23T15:01:08.105022600Z
     */
    public static class StripeWebhookEvent {
        public static final String ACCEPT_WEBHOOK = "payment-service-stripe-accept-webhook";

        public static final String TEST_WEBHOOK = "payment-service-stripe-test-webhook";

        private StripeWebhookEvent() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-04-23T15:01:08.118612300Z
     */
    public static class AuthorizeDotNetWebhooks {
        public static final String CREATE = "payment-service-authorizedotnet-webhooks-create";

        public static final String GET_ALL = "payment-service-authorizedotnet-webhooks-get-all";

        public static final String GET = "payment-service-authorizedotnet-webhooks-get";

        public static final String PUT = "payment-service-authorizedotnet-webhooks-put";

        public static final String DELETE = "payment-service-authorizedotnet-webhooks-delete";

        private AuthorizeDotNetWebhooks() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-04-23T15:01:08.119610700Z
     */
    public static class WebhookInbox {
        public static final String GET_WEBHOOK_INBOX_BY_ID = "payment-service-inbox-get-webhook-inbox-by-id";

        public static final String PROCESS_WEBHOOK_INBOX_BY_ID = "payment-service-inbox-process-webhook-inbox-by-id";

        private WebhookInbox() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-04-23T15:01:08.119610700Z
     */
    public static class Payment {
        public static final String GET_ALL_PAYMENTS = "payment-service-Payment-get-all-payments";

        public static final String GET_PAYMENTS_BY_ORDER_ID = "payment-service-Payment-get-payments-by-order-id";

        public static final String SUBMIT_PAYMENT = "payment-service-Payment-submit-payment";

        public static final String GET_PAYMENT_TOKEN = "payment-service-Payment-get-payment-token";

        public static final String DELETE_PAYMENT = "payment-service-Payment-delete-payment";

        public static final String TEST_LOG = "payment-service-Payment-test-log";

        public static final String REFUND_PAYMENT = "payment-service-Payment-refund-payment";

        public static final String REFUND_PAYMENT_OFFLINE = "payment-service-Payment-refund-payment-offline";

        public static final String GET_ALL_REFUND_TRANSACTIONS = "payment-service-Payment-get-all-refund-transactions";

        public static final String GET_REFUND_TRANSACTIONS_BY_ORDER_ID = "payment-service-Payment-get-refund-transactions-by-order-id";

        private Payment() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-04-23T15:01:08.119610700Z
     */
    public static class LoggedInUserPayment {
        public static final String GET_PAYMENTS_BY_ORDER_ID = "payment-service-me-get-payments-by-order-id";

        public static final String SUBMIT_PAYMENT = "payment-service-me-submit-payment";

        public static final String GET_PAYMENT_TOKEN = "payment-service-me-get-payment-token";

        private LoggedInUserPayment() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-04-23T15:01:08.119610700Z
     */
    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS = "payment-service-permissions-seed-roles-to-all-realms";

        public static final String SEED_ROLES_BY_REALM = "payment-service-permissions-seed-roles-by-realm";

        private Permission() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-04-23T15:01:08.119610700Z
     */
    public static class ElavonWebhookEvent {
        public static final String GET_IP = "payment-service-elavon-get-ip";

        private ElavonWebhookEvent() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-04-23T15:01:08.119610700Z
     */
    public static class AuthorizeDotNetWebhookEvent {
        public static final String ACCEPT_WEBHOOK = "payment-service-authorizedotnet-accept-webhook";

        private AuthorizeDotNetWebhookEvent() {
        }
    }
}
