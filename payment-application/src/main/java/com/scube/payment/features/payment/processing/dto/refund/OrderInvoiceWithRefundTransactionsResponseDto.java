package com.scube.payment.features.payment.processing.dto.refund;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.payment.features.payment.processing.dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderInvoiceWithRefundTransactionsResponseDto {
    private OrderInvoiceResponse orderInvoice;
    private List<GetPaymentResponseDto> payments;
    private List<RefundTransaction> refundTransactions;
}
