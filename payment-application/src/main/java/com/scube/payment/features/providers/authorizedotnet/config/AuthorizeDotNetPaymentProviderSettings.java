package com.scube.payment.features.providers.authorizedotnet.config;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuthorizeDotNetPaymentProviderSettings {
    @JsonAnyGetter
    @JsonAnySetter
    private Map<String, Object> properties;
}