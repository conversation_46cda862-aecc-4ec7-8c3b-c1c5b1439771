package com.scube.payment.features.providers.authorizedotnet.exchange;

import com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment.GetHostedPaymentPageTokenRequestDto;
import com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment.GetHostedPaymentPageTokenResponseDto;
import com.scube.payment.features.providers.authorizedotnet.dto.transaction.CreateTransactionRequestDto;
import com.scube.payment.features.providers.authorizedotnet.dto.transaction.CreateTransactionResponseDto;
import com.scube.payment.features.providers.authorizedotnet.dto.transaction.GetTransactionDetailsRequestDto;
import com.scube.payment.features.providers.authorizedotnet.dto.transaction.GetTransactionDetailsResponseDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange("/xml/v1/request.api")
public interface IAuthorizeDotNetXmlExchange {
    @PostExchange
    GetTransactionDetailsResponseDto getTransactionDetails(@RequestBody GetTransactionDetailsRequestDto request);

    @PostExchange
    GetHostedPaymentPageTokenResponseDto getHostedPaymentPageToken(@RequestBody GetHostedPaymentPageTokenRequestDto request);

    @PostExchange
    CreateTransactionResponseDto createTransaction(@RequestBody CreateTransactionRequestDto request);
}
