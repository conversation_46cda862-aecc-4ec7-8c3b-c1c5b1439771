package com.scube.payment.features.providers.authorizedotnet.controller;

import com.scube.auth.library.ITokenService;
import com.scube.lib.misc.annotations.validation.NoValidation;
import com.scube.multi.tenant.TenantContext;
import com.scube.payment.config.MySecurityExpressionRoot;
import com.scube.payment.features.providers.authorizedotnet.dto.webhook.AuthorizeDotNetWebhookEventDto;
import com.scube.payment.features.providers.authorizedotnet.service.AuthorizeDotNetWebhookEventService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/authorizedotnet")
@Slf4j
@Validated
@Profile("!test")
public class AuthorizeDotNetWebhookEventController {
    private final AuthorizeDotNetWebhookEventService authorizeDotNetWebhookEventService;
    private final ITokenService tokenService;

    /**
     * Accepts a webhook from Authorize.Net and stores it in the database.
     *
     * @param tenant             The tenant id that the webhook is for.
     * @param authorizeDotNetDto The webhook data that was sent by Authorize.Net
     * @param request            This is used by the @PreAuthorize annotation {@link MySecurityExpressionRoot#hasValidAuthorizeDotNetSignature(HttpServletRequest)}
     */
    @PostMapping("/webhook/{tenant}")
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize("hasValidSignature('Authorize.Net', #request, #tenant)")
    public void acceptWebhook(@PathVariable @NoValidation String tenant,
                              @RequestBody @Validated AuthorizeDotNetWebhookEventDto authorizeDotNetDto,
                              HttpServletRequest request) {
        TenantContext.setTenantId(tenant);

        tokenService.getNewTokenAndAuthenticate(tenant);

        authorizeDotNetWebhookEventService.store(authorizeDotNetDto);
        TenantContext.clear();
    }
}