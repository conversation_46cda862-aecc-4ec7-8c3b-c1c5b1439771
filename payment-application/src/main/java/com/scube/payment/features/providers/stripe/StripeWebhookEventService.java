package com.scube.payment.features.providers.stripe;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.config_utils.app_property.AppPropertyValue;
import com.scube.payment.features.webhook.IPaymentProviderWebhookService;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import com.scube.payment.features.webhook.inbox.service.WebhookInboxStorageService;
import com.stripe.Stripe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;

import static com.scube.payment.features.providers.stripe.StripeProperties.PAYMENT_PROVIDER_NAME;

@Slf4j
@Service(PAYMENT_PROVIDER_NAME)
@RequiredArgsConstructor
@Profile("!test")
public class StripeWebhookEventService implements IPaymentProviderWebhookService<StripeEvent> {
    private final StripeGateway stripeGateway;
    private final WebhookInboxStorageService webhookInboxStorageService;
    private final ObjectMapper objectMapper;

    @AppPropertyValue
    private StripeProperties stripeProperties;

    public void processWebhook(StripeEvent stripeEvent) {
        switch (stripeEvent.getType()) {
            case "checkout.session.completed":
                stripeGateway.authCapture(stripeEvent);
                break;
            case "refund.updated":
                stripeGateway.refund(stripeEvent);
                break;
//            case  "charge.refunded", "charge.refund.updated", "refund.created":
//                // Skipping for these events, since we need to update only one time for a refund.
//                break;
            case "refund.failed":
                stripeGateway.refundFailed(stripeEvent);
                break;
            case "charge.failed":
                stripeGateway._void(stripeEvent);
                break;
            default:
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unknown event type: " + stripeEvent.getType());
        }
    }

    @Override
    public void store(StripeEvent stripeEvent) {
        webhookInboxStorageService.store(PAYMENT_PROVIDER_NAME, stripeEvent, stripeEvent.getId());
    }

    public void store(String payload) {
        Stripe.apiKey = stripeProperties.getSecretKey();

        StripeEvent stripeEvent = new StripeEvent(payload);

        store(stripeEvent);
    }

    @Override
    public void process(WebhookInbox inbox) {
        Map<String, Object> payload = inbox.getPayload();
        StripeEvent dto = objectMapper.convertValue(payload, StripeEvent.class);
        processWebhook(dto);
    }
}
