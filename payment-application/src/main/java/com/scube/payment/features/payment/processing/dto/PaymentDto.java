package com.scube.payment.features.payment.processing.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.storage.model.Payee;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Data
public class PaymentDto {
    @JsonIgnore
    private Long id;
    private UUID paymentUuid;
    private UUID orderId;
    private BigDecimal amount;
    private String transactionId;
    private String paymentNumber;
    private PaymentStatus status;
    private String paymentProvider;
    private String paymentType;
    private Instant transactionDate;
    private UUID receiptId;
    private String paymentReference;
    private Payee payee;
    @JsonUnwrapped
    private AuditDto auditDto;
}
