drop function if exists license.fn_search_merge_request;
CREATE OR REPLACE FUNCTION license.fn_search_merge_request(
    IN fn_license_number varchar(255),
    IN fn_tag_number varchar(255),
    IN fn_requested_user_id uuid
)
RETURNS TABLE (
	"existingUserId" varchar(255),
	"probability" int,
	"matchType" Text
)
LANGUAGE plpgsql
AS $$
BEGIN
    fn_license_number := remove_any_extra_spaces_and_trim(fn_license_number);
    fn_tag_number := remove_any_extra_spaces_and_trim(fn_tag_number);
    -- replace any tag number that is 2024-0* to empty string
    fn_tag_number := regexp_replace(fn_tag_number, '^2024-0*', '');
    fn_requested_user_id := remove_any_extra_spaces_and_trim(fn_requested_user_id::text);

    RETURN QUERY
        select distinct
            pIndividual.entity_id::varchar(255) as "entityId",
            100 as "probability",
            'licenseNumber' as "matchType"
        from license lic

        --individual
        inner join association aIndividual
            on aIndividual.parent_association_type ILIKE 'LICENSE'
           AND aIndividual.child_association_type ILIKE 'PARTICIPANT'
           AND aIndividual.parent_id = lic.license_id

        inner join view_participant pIndividual
            on pIndividual.participant_id = aIndividual.child_id
            and pIndividual.group_name ILIKE 'Individual'

        --dog
        inner join association aDog
            on aDog.parent_association_type ILIKE 'LICENSE'
           AND aDog.child_association_type ILIKE 'PARTICIPANT'
           AND aDog.parent_id = lic.license_id

        inner join view_participant pDog
            on pDog.participant_id = aDog.child_id
            and pDog.group_name ILIKE 'Dog'

        where (lic.license_number ILIKE fn_license_number and COALESCE(fn_license_number, '') != '')
    UNION all
        select distinct
            pIndividual.entity_id::varchar(255) as "entityId",
            100 as "probability",
            'tagNumber' as "matchType"
        from license lic

        --individual
        inner join association aIndividual
            on aIndividual.parent_association_type ILIKE 'LICENSE'
           AND aIndividual.child_association_type ILIKE 'PARTICIPANT'
           AND aIndividual.parent_id = lic.license_id

        inner join view_participant pIndividual
            on pIndividual.participant_id = aIndividual.child_id
            and pIndividual.group_name ILIKE 'Individual'

        --dog
        inner join association aDog
            on aDog.parent_association_type ILIKE 'LICENSE'
           AND aDog.child_association_type ILIKE 'PARTICIPANT'
           AND aDog.parent_id = lic.license_id

        inner join view_participant pDog
            on pDog.participant_id = aDog.child_id
            and pDog.group_name ILIKE 'Dog'

        where (pDog.properties->>'tagNumber' ILIKE fn_tag_number and COALESCE(fn_tag_number, '') != '')

    UNION all

        select distinct
            ex.entity_id::varchar(255) as "entityId",
            60 as "probability",
            'firstName,lastName,address' as "matchType"
        from view_participant p

        --associations join address
        LEFT JOIN association a
            ON a.parent_association_type = 'PARTICIPANT'
            AND a.child_association_type = 'ADDRESS'
            AND a.parent_id = p.participant_id
        LEFT JOIN address addr
            ON a.child_id = addr.address_id

        inner join lateral(
             select entity_id from get_individuals(
                p.properties->>'firstName',
                p.properties->>'lastName',
                null,
                null,
                addr.street_address,
                null,
                null,
                null,
                null
            )
            where entity_id::uuid != fn_requested_user_id::uuid
        ) as ex on true

        where p.group_name = 'Individual'
        and p.entity_id = fn_requested_user_id

    UNION all
        select distinct
            ex.entity_id::varchar(255) as "entityId",
            50 as "probability",
            'firstName,lastName' as "matchType"
        from view_participant p

        inner join lateral(
             select entity_id from get_individuals(
                p.properties->>'firstName',
                p.properties->>'lastName',
                null,
                null,
                null,
                null,
                null,
                null,
                null
            )
            where entity_id::uuid != fn_requested_user_id::uuid
        ) as ex on true

        where p.group_name = 'Individual'
        and p.entity_id = fn_requested_user_id
        order by "probability" desc;
END;
$$;

--SELECT * FROM fn_search_merge_request('5005', '123','5ef72144-c4a0-41c5-aaf3-5924f0bb8be9');