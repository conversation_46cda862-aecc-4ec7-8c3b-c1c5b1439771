package com.scube.coordinator.features.cart;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.cart.dto.AddItemToCartRequest;
import com.scube.coordinator.features.cart.dto.AddItemToCartResponse;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/cart")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class CartController {
    private final CartService cartService;

    @PostMapping("/{cartId}/add")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Cart.ADD_TO_CART)
    public AddItemToCartResponse addToCart(@PathVariable("cartId") UUID cartId, @Valid @RequestBody AddItemToCartRequest addItemToCartRequest) {
        return cartService.addToCart(cartId, addItemToCartRequest, false);
    }

    @GetMapping("/{cartId}/infer-payee")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Cart.INFER_PAYEE)
    public ResponseEntity<?> inferPayee(@PathVariable("cartId") UUID cartId) {
        return ResponseEntity.ok(cartService.inferPayee(cartId, false));
    }
}