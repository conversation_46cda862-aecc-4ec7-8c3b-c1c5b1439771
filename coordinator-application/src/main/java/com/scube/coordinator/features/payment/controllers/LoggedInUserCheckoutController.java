package com.scube.coordinator.features.payment.controllers;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.payment.dto.CheckoutDto;
import com.scube.coordinator.features.payment.dto.CheckoutNoPaymentDueDto;
import com.scube.coordinator.features.payment.dto.CheckoutNoPaymentDueResponseDto;
import com.scube.coordinator.features.payment.services.PaymentService;
import com.scube.coordinator.features.permission.Permissions;
import com.scube.payment.features.providers.gateway.gen_dto.PaymentTokenResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("me/checkout")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class LoggedInUserCheckoutController {
    private final PaymentService paymentService;

    @PostMapping
    @RolesAllowed(Permissions.LoggedInUserCheckout.CHECKOUT)
    public PaymentTokenResponse checkout(@Valid @RequestBody CheckoutDto checkoutRequest) {
        return paymentService.checkout(checkoutRequest, true);
    }

    @PostMapping("/no-payment")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserCheckout.CHECKOUT_NO_PAYMENT_DUE)
    public CheckoutNoPaymentDueResponseDto checkoutNoPaymentDue(@Valid @RequestBody CheckoutNoPaymentDueDto checkoutRequest, @AuthenticationPrincipal OpenidClaimSet jwt) {
        return paymentService.checkout(checkoutRequest, jwt);
    }
}
