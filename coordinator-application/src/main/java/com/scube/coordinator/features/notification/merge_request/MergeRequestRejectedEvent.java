package com.scube.coordinator.features.notification.merge_request;

import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.Data;

import java.util.UUID;

@Data
public class MergeRequestRejectedEvent implements IRabbitFanoutSubscriber {
    private UUID requestedUserId;
    private String searchBy;
    private String searchValue;
    private String reason;
    private String deniedComment;
}