package com.scube.coordinator.features.notification.merge_request;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.coordinator.features.notification.GenerateTextCommand;
import com.scube.coordinator.features.notification.exceptions.GenerateEmailMessageCommandException;
import com.scube.coordinator.features.participant.ParticipantService;
import com.scube.notification.client.model.Email;
import com.scube.notification.client.rabbit.ScheduleEmailFanoutEvent;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class MergeRequestRejectedEventSendEmailHandler extends FanoutListener<MergeRequestRejectedEvent> {
    private final ParticipantService participantService;
    private final AmqpGateway amqpGateway;
    private final ObjectMapper objectMapper;

    @Override
    public void consume(MergeRequestRejectedEvent event) {
        var unFormattedDataString = participantService.getParticipantForEmail(event.getRequestedUserId());
        log.info("Sending email to participant: {}", unFormattedDataString);

        var data = new MergeRequestEmailBodyData(unFormattedDataString, event);

        if (ObjectUtils.isEmpty(data.getResidentEmail())) return;

        var emailData = objectMapper.valueToTree(data);

        var emailTemplateName = "MergeRequestRejectionEmailBody";
        var emailSubjectTemplateName = "MergeRequestRejectionEmailSubject";

        var emailBodyResult = amqpGateway.queryResult(new GenerateTextCommand(emailTemplateName, emailData));
        if (emailBodyResult == null || !emailBodyResult.isSuccess())
            throw new GenerateEmailMessageCommandException();

        var emailSubjectResult = amqpGateway.queryResult(new GenerateTextCommand(emailSubjectTemplateName, emailData));
        if (emailSubjectResult == null || !emailSubjectResult.isSuccess())
            throw new GenerateEmailMessageCommandException();

        var body = emailBodyResult.getResult();
        var subject = emailSubjectResult.getResult();

        amqpGateway.publish(
                ScheduleEmailFanoutEvent.builder()
                        .tag("RejectedMergeRequest")
                        .topic("email")
                        .createdBy("CoordinatorService")
                        .email(
                                Email.builder()
                                        .to(data.getResidentEmail())
                                        .from("<EMAIL>")
                                        .subject(subject)
                                        .body(body)
                                        .contentType("text/html")
                                        .build()
                        ).build()
        );
    }
}