package com.scube.coordinator.features.fees;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.fees.dto.AddFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.RemoveFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.UpdateFeeOnCartItemRequest;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/fees/cartItem")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class CartItemFeesController {
    private final CartItemFeesService feesService;

    @PatchMapping("/{cartItemId}/update")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.CartItemFees.UPDATE_FEE_ON_CART_ITEM)
    public Object updateFeeOnCartItem(@PathVariable final Long cartItemId, @RequestBody UpdateFeeOnCartItemRequest request) {
        return feesService.updateFeeOnCartItem(cartItemId, request);
    }

    @PostMapping("/{cartItemId}/addFee")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.CartItemFees.ADD_FEE_ON_CART_ITEM)
    public Object addFeeOnCartItem(@PathVariable final Long cartItemId, @RequestBody AddFeeOnCartItemRequest request) {
        return feesService.addFeeOnCartItem(cartItemId, request);
    }

    @PostMapping("/{cartItemId}/removeFee")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.CartItemFees.REMOVE_FEE_ON_CART_ITEM)
    public Object removeFeeOnCartItem(@PathVariable final Long cartItemId, @RequestBody RemoveFeeOnCartItemRequest request) {
        return feesService.removeFeeOnCartItem(cartItemId, request);
    }
}