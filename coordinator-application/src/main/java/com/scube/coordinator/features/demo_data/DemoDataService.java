package com.scube.coordinator.features.demo_data;

import com.scube.calculation.dto.gen_dto.CartInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.coordinator.features.cart.CartService;
import com.scube.coordinator.features.cart.dto.AddItemToCartRequest;
import com.scube.coordinator.features.payment.dto.CoordinatorPayeeDto;
import com.scube.coordinator.features.payment.dto.CoordinatorSubmitPaymentRequestByCartDto;
import com.scube.coordinator.features.payment.services.PaymentService;
import com.scube.licensing.features.license.gen_dto.CreateLicenseResponse;
import com.scube.licensing.features.participant.dto.gen_dto.CreateParticipantResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.datafaker.Faker;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class DemoDataService {
    private final LicenseServiceHttpExchange licenseServiceConnection;
    private final CartService cartService;
    private final CalculationServiceConnection calculationServiceConnection;
    private final PaymentService paymentService;

    @Async
    public CompletableFuture<Map<String, Object>> generateDemoDataAsync() {
        Faker faker = new Faker();
        CreateParticipantResponseDTO participant = createParticipant(faker);
        CreateLicenseResponse license = createLicense(faker, participant.getEntityId());
        makePaymentOnLicense(faker, license.getLicenseEntityId());
        var result = Map.of(
                "participant", participant,
                "license", license
        );
        log.info("Generated demo data: {}", result);
        return CompletableFuture.completedFuture(result);
    }

    private CreateParticipantResponseDTO createParticipant(Faker faker) {
        var fields = new LinkedMultiValueMap<String, String>();
        fields.add("suffix", faker.name().suffix());
        fields.add("firstName", faker.name().firstName());
        fields.add("lastName", faker.name().lastName());
        fields.add("middleName", faker.name().firstName());
        fields.add("dateOfBirth", faker.timeAndDate().birthday(18, 65).toString());
        fields.add("email", faker.internet().emailAddress());
        fields.add("phone", faker.phoneNumber().phoneNumber());
        fields.add("address", faker.address().streetAddress());
        fields.add("address2", faker.address().secondaryAddress());
        fields.add("city", faker.address().city());
        var state = faker.address().stateAbbr();
        fields.add("state", state);
        fields.add("zip", faker.address().zipCodeByState(state));
        var mailingSameAsPrimary = faker.bool().bool();
        fields.add("mailingSameAsPrimary", String.valueOf(mailingSameAsPrimary));
        if (mailingSameAsPrimary) {
            fields.add("mailaddress", faker.address().streetAddress());
            fields.add("mailaddress2", faker.address().secondaryAddress());
            fields.add("mailcity", faker.address().city());
            var mailingState = faker.address().stateAbbr();
            fields.add("mailstate", mailingState);
            fields.add("mailzip", faker.address().zipCodeByState(mailingState));
        }
        return licenseServiceConnection.createResident(fields);
    }

    private CreateLicenseResponse createLicense(Faker faker, UUID participantId) {
        var duration = faker.number().numberBetween(1, 3);
        var fields = new LinkedMultiValueMap<String, String>();
        fields.add("dogSex", faker.options().option("Male", "Female"));
        fields.add("dogName", faker.dog().name());
        fields.add("dogBreed", faker.dog().breed());
        fields.add("tagNumber", faker.bothify("??????").toUpperCase());
        fields.add("vaccineName", faker.options().option("Rabies"));

        LocalDate birthDate = faker.timeAndDate().birthday(1, 10);
        fields.add("dogBirthDate", birthDate.toString());

        // administeredDate must be in past
        Instant administeredDate = faker.timeAndDate().past(365, 1, TimeUnit.DAYS);
        fields.add("vaccineAdministeredDate", administeredDate.toString());

        // dueDate must be 1 year up to 3 years from administeredDate
        var dueDate = faker.timeAndDate().between(
                administeredDate.plusSeconds(TimeUnit.DAYS.toSeconds(365)),
                administeredDate.plusSeconds(TimeUnit.DAYS.toSeconds(365L * 3L))
        );
        fields.add("vaccineDueDate", dueDate.toString());

        fields.add("veterinaryName", faker.company().name() + " Veterinary Clinic");
        fields.add("dogPrimaryColor", faker.color().name());
        fields.add("microchipNumber", String.valueOf(faker.number().numberBetween(100000, 999999)));
        fields.add("dogSecondaryColor", faker.color().name());
        fields.add("vaccineDatesExempt", String.valueOf(faker.bool().bool()));
        fields.add("dogSpayedOrNeutered", faker.options().option("yes", "no"));
        fields.add("spayNeuterExemption", String.valueOf(faker.bool().bool()));

        return licenseServiceConnection.createLicense(participantId, "dogLicense", fields, duration);
    }

    private void makePaymentOnLicense(Faker faker, UUID licenseEntityId) {
        var addItemToCartRequest = new AddItemToCartRequest();
        addItemToCartRequest.setItemType("license");
        addItemToCartRequest.setItemId(licenseEntityId);
        cartService.addToCart(getActiveCart().getCartId(), addItemToCartRequest, false);
        var activeCart = getActiveCart(); // refetch the cart
        var paymentRequest = createPaymentRequest(faker, activeCart);
        paymentService.submitPaymentByCart(paymentRequest, false);
    }

    private static @NotNull CoordinatorSubmitPaymentRequestByCartDto createPaymentRequest(Faker faker, CartInvoiceResponse activeCart) {
        var request = new CoordinatorSubmitPaymentRequestByCartDto();
        request.setAmount(activeCart.getTotal());
        request.setCartId(activeCart.getCartId());
        var cartItems = activeCart.getItems();
        var paymentItems = new ArrayList<CoordinatorSubmitPaymentRequestByCartDto.PaymentRequestCartItemDto>();
        for (var item : cartItems) {
            var paymentItem = new CoordinatorSubmitPaymentRequestByCartDto.PaymentRequestCartItemDto();
            paymentItem.setItemType(item.getItemType());
            paymentItem.setItemId(String.valueOf(item.getItemId()));
            paymentItems.add(paymentItem);
        }
        request.setItems(paymentItems);
        var payee = createPayee(faker);
        request.setPayee(payee);
        request.setPaymentType(faker.options().option("card", "cash"));
        return request;
    }

    private static @NotNull CoordinatorPayeeDto createPayee(Faker faker) {
        var result = new CoordinatorPayeeDto();
        result.setBusinessName(faker.company().name());
        result.setEmail(faker.internet().emailAddress());
        result.setFirstName(faker.name().firstName());
        result.setLastName(faker.name().lastName());
        result.setMailingAddress(faker.address().streetAddress());
        result.setMailingAddress2(faker.address().secondaryAddress());
        result.setMailingCity(faker.address().city());
        var mailingState = faker.address().stateAbbr();
        result.setMailingState(mailingState);
        result.setMailingZipCode(faker.address().zipCodeByState(mailingState));
        result.setPhone(faker.phoneNumber().phoneNumber());
        return result;
    }

    private CartInvoiceResponse getActiveCart() {
        return calculationServiceConnection.cart().getActiveCart();
    }
}