package com.scube.coordinator.features.contact_us;

import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class GetGeneratedCodeQuery implements IRabbitFanoutPublisherRpc<GetGeneratedCodeQueryResponse> {
    private String entityType;
    private String entityId;
    private String action = "lookup";

    public GetGeneratedCodeQuery(String entityType, String entityId) {
        this.entityType = entityType;
        this.entityId = entityId;
    }

    public GetGeneratedCodeQuery(String entityType, String entityId, String action) {
        this.entityType = entityType;
        this.entityId = entityId;
        this.action = action;
    }
}
