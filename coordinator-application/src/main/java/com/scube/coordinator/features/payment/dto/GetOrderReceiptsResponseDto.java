package com.scube.coordinator.features.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderReceiptsResponseDto {
    private UUID orderId;
    private List<ReceiptDto> receipts;

    @Builder
    @Data
    public static class ReceiptDto {
        private String paymentId;
        private Instant transactionDate;
        private String receiptUrl;
    }
}



