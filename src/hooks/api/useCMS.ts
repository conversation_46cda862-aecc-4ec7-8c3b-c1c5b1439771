// Type definitions for the Landing Page
interface Button {
  id: number;
  label: string;
  href: string;
  hero_id?: string | null;
  contact_id?: number | null;
}

interface DirectusImage {
  id: string;
  storage: string;
  filename_disk: string;
  filename_download: string;
  title: string;
  type: string;
  folder: string;
  uploaded_by: string;
  uploaded_on: string;
  modified_by: string;
  modified_on: string;
  charset: string | null;
  filesize: string;
  width: number;
  height: number;
  duration: string | null;
  embed: string | null;
  description: string | null;
  location: string | null;
  tags: string | null;
  metadata: Record<string, unknown>;
  focal_point_x: string | null;
  focal_point_y: string | null;
}

interface Hero {
  id: string;
  header: string;
  description: string;
  collection_id: Collection;
  buttons: Button[];
  hero_image: DirectusImage;
}

interface Overview {
  id: number;
  image: DirectusImage;
  overview_header: string;
  overview_description: string;
  SR_collection_id: number;
}

interface Service {
  id: number;
  mini_text: string;
  header: string;
  description: string;
  collection_id: Collection;
  overview: Overview[];
}

interface ContactUs {
  id: number;
  header: string;
  description: string;
  collection_id: Collection;
  button: Button[];
}

interface PageContent {
  id: number;
  about_id: number;
  title: string;
  content: string;
}

interface AboutUs {
  id: number;
  collection_id: Collection;
  page_content: PageContent[];
}

interface Collection {
  id: string;
  hero: string[];
  services: number[];
  contact_us: number[];
  about_us: number[];
}

interface LandingPageData {
  id: string;
  hero: Hero[];
  services: Service[];
  contact_us: ContactUs[];
  about_us: AboutUs[];
}

interface LandingPage {
  data: LandingPageData;
}

// Type definitions for the Terms of Service
interface TermsContent {
  id: string;
  title: string;
  content: string;
  collection_id?: string | null;
  terms_collection_id: string;
}

interface TermsOfService {
  data: TermsOfServiceData;
}

// Type definitions for the Privacy Policy
interface PrivacyContent {
  id: string;
  title: string;
  content: string;
  collection_id: string;
  terms_collection_id?: string | null;
}

interface PrivacyPolicy {
  data: PrivacyPolicyData;
}

import { requests } from "@/utils/agent";
// Hook for CMS data fetching
import { useQuery } from "@tanstack/react-query";

// CMS Link URLS
const build: {
  [key: string]: string;
} = {
  localhost: "https://cms-dev.clerkxpress.com/items",
  development: "https://cms-dev.clerkxpress.com/items",
  production: "https://cms.clerkxpress.com/items",
  staging: "https://cms-staging.clerkxpress.com/items",
  default: "https://cms-dev.clerkxpress.com/items",
};

const controller = build[process.env.NEXT_PUBLIC_APP_ENV || "default"];

const dataPath: { [key: string]: string } = {
  privacy_policy: "/privacy_policy?fields=*.*",
  terms_of_service: "/terms_of_service?fields=*.*",
  landing_page: "/landing_page?fields=*.*.*.*",
  faq: "/faq?fields=*.*.*",
};

interface PrivacyContent {
  id: string;
  title: string;
  content: string;
  collection_id: string;
  terms_collection_id?: string | null;
}

export interface PrivacyPolicyData {
  id: string;
  date_created: string;
  date_updated: string;
  page_content: PrivacyContent[];
}

interface PrivacyPolicy {
  data: PrivacyPolicyData;
}

interface TermsContent {
  id: string;
  title: string;
  content: string;
  collection_id?: string | null;
  terms_collection_id: string;
}

export type TermsOfServiceData = {
  id: string;
  date_created: string;
  date_updated?: string | null;
  page_content: TermsContent[];
};

interface TermsOfService {
  data: TermsOfServiceData;
}

interface FAQContent {
  id: string;
  collection_id: string;
  Header: string;
  Content: string;
  scribe_link: string;
}

interface FAQSection {
  id: string;
  Section_Header: string;
  faq_id: {
    id: string;
    order: string;
    faq_content: string[];
  };
  content: FAQContent[];
}

interface FAQData {
  id: string;
  order: string;
  faq_content: FAQSection[];
}

interface FAQ {
  data: FAQData;
}

export interface ParsedFAQ {
  section: string;
  data: {
    title: string;
    content: string;
  }[];
}

// Function to parse FAQ data
const parseFAQData = (
  faq: FAQ,
): {
  [section: string]: {
    [title: string]: {
      title: string;
      content: string;
      scribe_link: string;
    };
  };
} => {
  return faq.data.faq_content.reduce(
    (acc, section) => {
      acc[section.Section_Header] = section.content.reduce(
        (contentAcc, contentItem) => {
          const scribe_link = contentItem.scribe_link;
          contentAcc[contentItem.Header] = {
            title: contentItem.Header,
            content: contentItem.Content,
            scribe_link: scribe_link,
          };

          return contentAcc;
        },
        {} as {
          [title: string]: {
            title: string;
            content: string;
            scribe_link: string;
          };
        },
      );
      return acc;
    },
    {} as {
      [section: string]: {
        [title: string]: {
          title: string;
          content: string;
          scribe_link: string;
        };
      };
    },
  );
};

// Type definitions for the new Landing Page structure
export type Landing_Page = {
  hero: {
    header: string;
    description: string;
    buttons: {
      label: string;
      href: string;
    }[];
    hero_image: string;
  };
  services: {
    mini_text: string;
    header: string;
    description: string;
    overview: {
      image_id: string;
      overview_header: string;
      overview_description: string;
    }[];
  };
  contact: {
    header: string;
    description: string;
    button: {
      label: string;
      href: string;
    };
  };
  about: {
    title: string;
    content: string;
  }[];
};

// Function to transform LandingPage to Landing_Page
const transformLandingPage = (landingPage: LandingPage): Landing_Page => {
  return {
    hero: landingPage.data.hero.map((h) => ({
      header: h.header,
      description: h.description,
      buttons: h.buttons.map((b) => ({
        label: b.label,
        href: b.href,
      })),
      hero_image: h.hero_image.id,
    }))[0],
    services: landingPage.data.services.map((s) => ({
      mini_text: s.mini_text,
      header: s.header,
      description: s.description,
      overview: s.overview.map((o) => ({
        image_id: o.image.id,
        overview_header: o.overview_header,
        overview_description: o.overview_description,
      })),
    }))[0],
    contact: landingPage.data.contact_us.map((c) => ({
      header: c.header,
      description: c.description,
      button: {
        label: c.button[0].label,
        href: c.button[0].href,
      },
    }))[0],
    about: landingPage.data.about_us[0].page_content.map((a) => ({
      title: a.title,
      content: a.content,
    })),
  };
};

// Get Table of Contents
type TableOfContentsResult = {
  id: string;
  label: string;
};

// process order
const processOrder = (order: string) => {
  // Return the order in array format, separated by \n
  return order.split("\n");
};

// Get Table of Contents
const getTableOfContents = (
  obj: PrivacyPolicyData | null,
  order: string[] = [],
  remove?: string[],
): TableOfContentsResult[] => {
  if (!obj) return [];

  // For every page content, get the id and title, in the order specified
  const tableOfContents = order.map((id) => {
    const content = obj.page_content.find(
      (content) => id.toLowerCase() === content.title.toLowerCase(),
    );

    return {
      id: content?.id ?? "",
      label: content?.title ?? "",
    };
  });

  return tableOfContents;
};

type MDXDataResult = {
  id: string;
  label: string;
  content: any;
}[];

// Get Mdx Content
const getMDXData = (
  obj: PrivacyPolicyData | null,
  order: string[] = [],
): MDXDataResult => {
  let mdxData: MDXDataResult = [];

  if (!obj) return mdxData;

  // For every page content, get the title and content
  mdxData = order.map((id) => {
    const content = obj.page_content.find(
      (content) => id.toLowerCase() === content.title.toLowerCase(),
    );

    return {
      id: content?.id ?? "",
      label: content?.title ?? "",
      content: content?.content ?? "",
    };
  });

  return mdxData;
};

export const useCMS = (key: string) => {

  return useQuery({
    queryKey: ["cms", key],
    queryFn: async () => {
      const response = await fetch(`${controller}${dataPath[key]}`);
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();

      if (key === "landing_page") {
        return transformLandingPage(data);
      } else if (key === "privacy_policy" || key === "terms_of_service") {
        const order = processOrder(data.data.order);
        const content = data.data;
        return {
          tableOfContents: getTableOfContents(content, order),
          mdxData: getMDXData(content, order),
          lastUpdate: content.date_updated,
        };
      } else if (key === "faq") {
        return parseFAQData(data);
      }
      return data;
    },
    staleTime: 1000 * 60 * 60 * 24,
  });
};
