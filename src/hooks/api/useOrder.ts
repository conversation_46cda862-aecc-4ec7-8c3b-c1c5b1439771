"use client";
import { useQuery } from "@tanstack/react-query";
import { requests } from "@/utils/agent";
import { useMyProfile } from "../providers/useMyProfile";

// Get order information
export const useGetOrder = (orderId: string) => {
  console.log("Running useGetOrder with", orderId);

  const { hasPermissions } = useMyProfile();
  const admin = hasPermissions(["super-admin"]);
  console.log(admin)

  return useQuery({
    queryKey: ["order", orderId],
    queryFn: () =>
      admin
        ? requests.get<any>(`/calculation/order/${orderId}`)
        : requests.get<any>(`/calculation/me/order/${orderId}`),
    enabled: !!orderId,
  });
};
