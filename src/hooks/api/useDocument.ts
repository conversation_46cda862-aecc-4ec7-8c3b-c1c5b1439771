"use client";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useMyProfile } from "../providers/useMyProfile";
import { requests } from "@/utils/agent";

// each individual query under a template
export interface Query {
  queryKey: string;
  referenceKey: string;
  type: "SQL" | "SERVICE";
  parameters: string[];
  sql: string | null;
}

// when formData is non‑empty it has these fields
export interface FormDataParameter {
  type: "dateRange" | "text" | "date";
  label?: string;
  params?: string[];
  defaultParam?: string;
  required?: boolean;
  fieldName?: string;
  validation?: {
    regex: string;
    message: string;
  };
}

export interface FormData {
  type?: string;
  // type of the form data EX: date, dateRange
  label?: string;
  // label of the form data EX: Daily Cash Report
  description?: string;
  // Description of the report
  exportTypes?: string[];
  // ex: pdf, xlsx, docx
  reportTypeId?: string;
  // only when there are parameters
  parameters?: FormDataParameter[];
  // allow empty object
  [key: string]: any;
}

// one template entry
export interface Template {
  templateUUID: string;
  name: string;
  description: string;
  documentUUID?: string;
  queries?: Query[];
  category?: string;
  subCategory?: string | null;
  fileType?: string;
  file?: File | null;
  isReport?: boolean;
  formData?: FormData | null;
}

// root response
export interface TemplatesResponse {
  templates: Template[];
}

// Get a list of all document templates
export const useGetAllDocumentTemplates = () => {
  const { hasPermissions } = useMyProfile();
  return useQuery({
    queryKey: ["documentTemplates"],
    queryFn: () =>
      hasPermissions(["super-admin"])
        ? requests.get<TemplatesResponse>(`/coordinator/templates/all`)
        : null,
  });
};

export const useGetDocumentTemplate = (templateUUID: string) => {
  const { hasPermissions } = useMyProfile();
  return useQuery({
    queryKey: ["documentTemplate", templateUUID],
    queryFn: () =>
      hasPermissions(["super-admin"])
        ? requests.get<Template>(`/coordinator/templates/${templateUUID}`)
        : null,
    enabled: !!templateUUID,
  });
};

// Simple update mutation that takes FormData directly
export const useUpdateDocumentTemplate = () => {
  const { hasPermissions } = useMyProfile();
  return useMutation({
    mutationFn: async ({
      data,
    }: {
      data: FormData;
    }) => {
      if (!hasPermissions(["super-admin"])) {
        throw new Error("Permission denied");
      }

      return requests.put<Template>(`/coordinator/templates`, data);
    },
  });
};

// Simple create mutation that takes FormData directly
export const useCreateDocumentTemplate = () => {
  const { hasPermissions } = useMyProfile();
  return useMutation({
    mutationFn: async (data: FormData) => {
      if (!hasPermissions(["super-admin"])) {
        throw new Error("Permission denied");
      }

      return requests.post<Template>(`/coordinator/templates`, data);
    },
  });
};

export const useDownloadDocumentFile = (documentUUID: string) => {
  const { hasPermissions } = useMyProfile();
  return useQuery({
    queryKey: ["getDocBlob", documentUUID],
    queryFn: () =>
      hasPermissions(["super-admin"]) && documentUUID
        ? requests.get<any>(
            `/document-service/download?documentUUID=${documentUUID}`,
            {
              responseType: "blob",
            },
          )
        : null,
    enabled: !!documentUUID,
  });
};
