import { requests } from "@/utils/agent";
import { useQuery } from "@tanstack/react-query";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

export const useGetServices = () => {
  return useQuery({
    queryKey: ["services"],
    queryFn: () => requests.get<any>(`/coordinator/health`),
  });
};


// Document Service Download
export const useGetDocumentBlob = (
  documentUuid: string | undefined | null,
  enabled?: boolean,
) => {
  const { hasPermissions } = useMyProfile();
  const admin = hasPermissions(["super-admin"])
  return useQuery({
    queryKey: ["getDocBlob", documentUuid],
    queryFn: () =>
      requests.get<any>(
        admin
          ? `/document-service/download?documentUUID=${documentUuid}`
          : `/document-service/me/download?documentUUID=${documentUuid}`,
        {
          responseType: "blob",
        },
      ),
    enabled: enabled && !!documentUuid
  });
};
