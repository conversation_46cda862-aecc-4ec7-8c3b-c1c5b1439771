import {
  useQuery,
  useMutation,
  UseMutationResult,
} from "@tanstack/react-query";
import { requests } from "@/utils/agent";
import { useRouter } from "next/navigation";
import { ActiveCartItems } from "@/types/CartType";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

type AddItemParams = {
  itemId: string;
  itemType: string;
  cartId: string;
};

type RemoveItemParams = {
  cartId: string;
  cartItemId: number;
};

type SubmitOrderOptions = {
  onSuccess?: (data: any, variables: any, context: any) => void;
  onError?: (error: any, variables: any, context: any) => void;
};

// Get Active Cart
export const useGetActiveCart = (paymentMethod?: string | null) => {
  const { hasPermissions } = useMyProfile();
  return useQuery({
    queryKey: ["activeCart", paymentMethod],
    queryFn: () =>{
      const paymentModeParam = paymentMethod ? `?paymentMethod=${paymentMethod}` : '';
      return hasPermissions(["super-admin"])
        ? requests.get<ActiveCartItems>(`/calculation/cart/active${paymentModeParam}`)
        : requests.get<ActiveCartItems>(`/calculation/me/cart/active${paymentModeParam}`);
    },
    staleTime: 0,
    cacheTime: 0,
  });
};

// Add Item to Cart
export const useAddItemToCart = () => {
  const { hasPermissions } = useMyProfile();
  return useMutation(({ itemId, itemType, cartId }: AddItemParams) =>
    hasPermissions(["super-admin"])
      ? requests.post<any>(`/coordinator/cart/${cartId}/add`, {
          itemType,
          itemId,
        })
      : requests.post<any>(`/coordinator/me/cart/${cartId}/add`, {
          itemType,
          itemId,
        }),
  );
};

// Create a new cart
export const useCreateCart = () => {
  const { hasPermissions } = useMyProfile();

  return useMutation(() =>
    hasPermissions(["super-admin"])
      ? requests.post<any>(`/calculation/cart/new`, {})
      : requests.post<any>(`/calculation/me/cart/new`, {}),
  );
};

//  Remove item from cart
export const useRemoveItemFromCart = () => {
  const { hasPermissions } = useMyProfile();

  return useMutation(({ cartId, cartItemId }: RemoveItemParams) => {
    return hasPermissions(["super-admin"])
      ? requests.del<any>(`/calculation/cart/${cartId}/item/${cartItemId}`)
      : requests.del<any>(`/calculation/me/cart/${cartId}/item/${cartItemId}`);
  });
};

// Clear cart
export const useClearCart = () => {
  return useMutation(() =>
    requests.patch<any>(`/calculation/me/cart/clear`, {}),
  );
};

export const useSubmitOrder = ({
  onSuccess,
  onError,
}: SubmitOrderOptions = {}) => {
  return useMutation(
    (order: any) =>
      requests.post<any>(
        `/coordinator/payment/processPaymentAndDeleteCartItems`,
        order,
      ),
    {
      onSuccess,
      onError,
    },
  );
};

// Get Payee information
export const useGetPayeeInfo = (cartId: string) => {
  console.log(cartId);
  return useQuery({
    queryKey: ["payeeInfo", cartId],
    queryFn: () => requests.get<any>(`/coordinator/cart/${cartId}/infer-payee`),
    enabled: !!cartId,
  });
};

export interface CheckoutResponse {
  token: string;
  refId: string;
  provider: "Authorize.Net" | "Stripe";
  providerProperties: {
    key: string;
  };
}

interface CheckoutBody {
  amount: number;
  cartId: string;
}

export const useGetResidentToken = (): UseMutationResult<
  CheckoutResponse,
  Error,
  CheckoutBody
> => {
  const {hasPermissions} = useMyProfile();
  return useMutation<CheckoutResponse, Error, CheckoutBody>(
    (body: CheckoutBody) => 
      hasPermissions(["super-admin"])
      ? requests.post<CheckoutResponse>(`/coordinator/checkout`, body)
      : requests.post<CheckoutResponse>(`/coordinator/me/checkout`, body),
    {
      onSuccess: (data: any) => {
        console.log("Successfully fetched resident token");
        return data;
      },
      onError: (error: Error) => {
        console.log("Error fetching resident token:", error);
      },
    },
  );
};

// No Payment
export const useNoPayment = (cartId: string) => {
  const { push } = useRouter();

  return useMutation(
    () =>
      requests.post<any>(`/coordinator/me/checkout/no-payment`, {
        cartId,
      }),
    {
      onSuccess: (data: any) => {
        console.log("Successfully set no payment");
        push(`/orderSuccess?orderId=${data.orderId}`);
      },
      onError: (error: any) => {
        console.log("Error setting no payment:", error);
        push(
          `/cart?error=${error.response.data.message}&status=${error.response.status.toString()}`,
        );
      },
    },
  );
};

// Fee Updates ----------------------------
export interface EditFeeProps {
  activityId: string | null; // Used for Add
  feeCode: string | null; // Used for Add
  feeAmount: number | null; // Used for Add, Update
  reason: string; // Used for Remove, Add, Update
  feeId: string | null; // Used for Remove, Update
  label: string | null; // Not used for anything other than setting state
}

// Update Fee Amount in Cart
export const useUpdateFeeAmount = (feeId:string) => {
  return useMutation(
    (body: {feeAmount: number; reason: string }) =>
      requests.patch<any>(
       // `/coordinator/fees/cartItem/${cartItemId}/update`,
       `/license/fees/${feeId}/update`,
        body,
      ),
  );
};

export const useAddFeeToCartItem = (cartItemId: number) => {
  return useMutation(
    (body: {
      activityId: string | null;
      reason: string;
      feeCode: string;
      feeAmount: number;
    }) =>
      requests.post<any>(
        `/coordinator/fees/cartItem/${cartItemId}/addFee`,
        body,
      ),
  );
};

export const useRemoveFeeFromCartItem = (cartItemId: number) => {
  return useMutation((body: { reason: string; feeId: string }) =>
    requests.post(`/coordinator/fees/cartItem/${cartItemId}/removeFee`, body),
  );
};
