import { Contact, Individual } from "@/types/IndividualType";
import {
  useUpdateIndividualContact,
} from "@/hooks/api/useProfiles";
import {
  EditDialog,
  EditDialogFooter,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import { useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { FormValues } from "@/types/DogType";
import { Comment, Emails } from "@/components/dialog/EditFields";

export const EditEmail = ({
  individual,
}: {
  individual: Individual;
}) => {
  const emails: Contact[] =
    individual?.contacts
      ?.filter((c: any) => c.type === "Email")
      .sort((a: Contact, b: Contact) => (a.group > b.group ? 1 : -1)) || [];

  const transformedObject: Record<number, string> = emails.reduce(
    (acc: any, current: any) => {
      acc[current?.id] = current.value;
      return acc;
    },
    {},
  );

  console.log(emails);

  const initialValues = {
    ...transformedObject,
    comment: "",
  };

  const updateContact = useUpdateIndividualContact();

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors, isDirty, isValid },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    updateContact.mutate(
      {
        entityId: individual.entityId,
        body: data,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Individual Updated",
            message: "Successfully Updated Individual Information",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  return (
    <EditDialog title="Edit Email" isOpen={isOpen} setIsOpen={setIsOpen}>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <Emails emails={emails} register={register} errors={errors} />
        <Comment register={register} />
        <EditDialogFooter
          handleCancel={handleCancel}
          disabled={!isDirty}
          loading={updateContact.isLoading}
        />
      </form>
    </EditDialog>
  );
};

export default EditEmail;
