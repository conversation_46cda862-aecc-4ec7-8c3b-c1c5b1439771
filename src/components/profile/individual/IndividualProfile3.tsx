import { Individual } from "@/types/IndividualType";
import AddressInformation from "./AddressInformation";
import PersonalInformation from "./PersonalInformation";

export default function IndividualProfile3({
  individual,
  title = "Profile",
  entityIsFetching, 
  entityRefetch
}: {
  individual: Individual;
  title?: string;
  entityIsFetching: boolean;
  entityRefetch: () => void;
}) {
  console.log(individual);

  return (
    <div className="flex flex-col gap-6">
      <div className="text-xl font-semibold text-neutral-700">{title}</div>
      <div className="flex flex-col gap-16">
        <PersonalInformation 
          individual={individual}
          entityIsFetching={entityIsFetching}
          entityRefetch={entityRefetch}
        />
        <AddressInformation 
          individual={individual}
          entityIsFetching={entityIsFetching}
          entityRefetch={entityRefetch}
        />
      </div>
    </div>
  );
}
