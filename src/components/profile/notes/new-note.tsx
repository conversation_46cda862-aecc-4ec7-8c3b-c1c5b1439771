import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useProfileNotes } from "./useProfileNotes";
import { Pin, Plus, Tag, User } from "lucide-react";
import { Input } from "@/components/ui/input";
import { MultiSelect } from "@/components/ui/multi-select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";

export default function NewNote() {
  const { possibleAssociations, addNote, handleAddNote, newNote, setNewNote } =
    useProfileNotes();

  return (
    <Card className="">
      <CardHeader>
        <CardTitle>Add New Note</CardTitle>
      </CardHeader>
      <CardContent>
        <Textarea
          placeholder="Enter note content..."
          value={newNote.note}
          onChange={(e) => setNewNote({ ...newNote, note: e.target.value })}
          className="mb-4"
        />
        <div className="mb-4 flex items-center gap-3">
          <Tag className="size-4" />
          <Input
            type="text"
            placeholder="Enter tags (comma-separated)"
            value={newNote.noteTags}
            onChange={(e) =>
              setNewNote({ ...newNote, noteTags: e.target.value })
            }
          />
        </div>

        {/* MultiSelect for Associations */}
        <div className="mb-4 flex items-center gap-3">
          <User className="size-4" />
          <MultiSelect
            options={possibleAssociations.map((assoc) => ({
              label: assoc.label,
              value: assoc.entityId,
            }))}
            onValueChange={(selectedValues) => {
              const updatedAssociations = possibleAssociations
                .filter((assoc) => selectedValues.includes(assoc.entityId))
                .map((assoc) => ({
                  entityId: assoc.entityId,
                  entityType: assoc.entityType,
                }));
              setNewNote({ ...newNote, associations: updatedAssociations });
            }}
            defaultValue={newNote.associations.map((assoc) => assoc.entityId)}
            placeholder="Add associations"
          />
        </div>

        {/* Pinned Note */}
        <div className="mb-4 flex items-center gap-3">
          <Pin className="size-4" />
          <div className="flex items-center gap-2">
            <Checkbox
              checked={newNote.pinned}
              onCheckedChange={(checked: boolean) => {
                console.log(checked);
                setNewNote({ ...newNote, pinned: checked });
              }}
              id="pinnedEdit"
            />

            <label htmlFor="pinnedEdit">Pin this note</label>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleAddNote}
          disabled={!newNote.note || addNote.isLoading}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Note
        </Button>
      </CardFooter>
    </Card>
  );
}
