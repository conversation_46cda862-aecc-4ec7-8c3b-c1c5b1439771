import {
  ProfileGrid,
  ProfileGridItem,
  ProfileSection,
  createFormData,
  ProfileCustomSelectInput,
  ProfileSelectInput,
  ProfileDocument,
  flagCheck,
} from "@/components/profile/helpers/Setup";
import { useUpdateEntityDogProfile } from "@/hooks/api/useProfiles";
import { Dog } from "@/types/DogType";
import { useGetSettingsByOption } from "@/hooks/api/useAdmin";
import { EditProvider, useEditContext } from "../context/EditContext";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const ServiceDogInformation = ({
  dog,
  entityRefetch,
  entityIsFetching,
}: {
  dog: Dog;
  entityRefetch: () => void;
  entityIsFetching: boolean;
}) => {
  const updateEntity = useUpdateEntityDogProfile();
  const defaultValues = {
    licenseExempt: dog.licenseExempt || false,
    serviceDogType: dog.serviceDogType || "",
    licenseExemptionDocument:
      dog?.documents?.find((doc) => doc.key === "licenseExemptionDocument") ||
      null,
  };

  return (
    <EditProvider
      entity={dog}
      entityId={dog.entityId}
      entityType={dog.entityType}
      entityRefetch={entityRefetch}
      entityIsFetching={entityIsFetching}
      defaultValues={defaultValues}
      updateEntity={updateEntity}
    >
      <Content updateEntity={updateEntity} />
    </EditProvider>
  );
};

export default ServiceDogInformation;

const Content = ({ updateEntity }: { updateEntity: any }) => {
  const { entity, entityId } = useEditContext();
  const { watchField } = useEditContext();

  // Select Options
  const {
    data: serviceDogTypes,
    error: serviceDogTypeError,
    isLoading: serviceDogTypesIsLoading,
  } = useGetSettingsByOption("entity", "dog", "service_dog");

  const onSubmit = (data: any) => {
    const newData: any = {
      ...data,
    };

    const formData = createFormData(newData);

    updateEntity.mutate({
      entityId: entityId,
      body: formData,
    });
  };

  const rejectedFields = entity?.rejectedFields || [];
  const { hasPermissions } = useMyProfile();

  return (
    <ProfileSection
      label="Service Dog Information"
      onSubmit={onSubmit}
      saving={updateEntity.isLoading}
      hideEdit={!entity.active}
    >
      <ProfileGrid cols={2}>
        <ProfileGridItem
          label="Service Dog"
          flagged={flagCheck("licenseExempt", rejectedFields)}
          field={"licenseExempt"}
          required
        >
          <ProfileSelectInput
            name="licenseExempt"
            options={[
              { label: "Yes", value: true },
              { label: "No", value: false },
            ]}
            required
          />
        </ProfileGridItem>
        {watchField("licenseExempt") && (
          <ProfileGridItem
            label={"Service Dog Type"}
            flagged={flagCheck("serviceDogType", rejectedFields)}
            field={"serviceDogType"}
            required={
              watchField("licenseExempt") &&
              watchField("licenseExempt") === true
            }
          >
            {serviceDogTypesIsLoading
              ? "Loading..."
              : serviceDogTypes && (
                  <ProfileCustomSelectInput
                    name="serviceDogType"
                    options={serviceDogTypes}
                    required={
                      watchField("licenseExempt") &&
                      watchField("licenseExempt") === true
                    }
                  />
                )}
          </ProfileGridItem>
        )}
      </ProfileGrid>
      {watchField("licenseExempt") && (
        <ProfileGrid cols={2}>
          <ProfileGridItem
            label="Service Dog Verification Document"
            flagged={flagCheck("licenseExemptionDocument", rejectedFields)}
            field={"licenseExemptionDocument"}
            required={!hasPermissions(["super-admin"])}
          >
            {
              <ProfileDocument
                name="licenseExemptionDocument"
                accept={[".pdf", ".jpeg", ".png", ".jpg"]}
                required={!hasPermissions(["super-admin"])}
              />
            }
          </ProfileGridItem>
        </ProfileGrid>
      )}
    </ProfileSection>
  );
};
