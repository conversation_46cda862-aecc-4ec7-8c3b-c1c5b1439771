import React from "react";
import { ActiveCartFee, ActiveCartItem } from "@/types/CartType";
import { LoaderIcon, MinusSquare, PlusSquare } from "lucide-react";
import { EditFeeProps } from "@/hooks/api/useCart";
import { useMyCart } from "@/hooks/useMyCart";
import Loading from "@/app/(app)/loading";

const ListCartFees = ({
  cartItem,
  setEditFee,
}: {
  cartItem: ActiveCartItem;
  setEditFee: (fee: EditFeeProps) => void;
}) => {
    if (!cartItem || !cartItem.fees) return null;
  const feesByActivity: { [key: string]: ActiveCartFee[] } = {};
  cartItem.fees.forEach((fee) => {
    const licenseActivityId = fee.licenseActivityId;
    if (!feesByActivity[licenseActivityId]) {
      feesByActivity[licenseActivityId] = [];
    }
    feesByActivity[licenseActivityId].push(fee);
  });

  const { cartIsFetching } = useMyCart();
  if (cartIsFetching) {
    console.log("test");
    return (
      <div className="flex h-32 items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <LoaderIcon className="h-8 w-8 animate-spin text-blue-500" />
          <span className="text-sm font-semibold text-gray-800">
            Loading fees...
          </span>
        </div>
      </div>
    );
  }

  console.log(cartItem);

  return (
    <div className="space-y-4 bg-white">
      {Object.keys(feesByActivity).map((activityId, index) => {
        const fees = feesByActivity[activityId];
        return (
          <div key={activityId} className="space-y-4">
            <FeeLabel cartItemType={cartItem.itemType} count={index + 1} />
            {fees.map((fee) => (
              <button
                key={fee.feeCode}
                className="flex w-full items-center justify-between rounded-lg bg-gray-100 px-4 py-3 transition-all hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                onClick={() =>
                  setEditFee({
                    activityId: fee.licenseActivityId ?? null,
                    feeCode: fee.feeCode,
                    feeAmount: fee.feeAmount,
                    reason: "",
                    feeId: fee.feeId,
                    label: fee.label,
                  })
                }
              >
                <span className="font-medium text-gray-800">{fee.label}</span>
                <span className="flex items-center gap-1 font-semibold text-gray-800">
                  {formatter.format(fee.feeAmount)}
                  {fee.feeAmount < 0 ? (
                    <MinusSquare className="text-red-500" />
                  ) : (
                    <PlusSquare className="text-green-600" />
                  )}
                </span>
              </button>
            ))}
            <button
              className="flex w-full items-center justify-center rounded-lg px-4 py-3 text-center transition-all hover:bg-blue-500 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              onClick={() =>
                setEditFee({
                  activityId,
                  feeCode: null,
                  feeAmount: 0,
                  reason: "",
                  feeId: null,
                  label: null,
                })
              }
            >
              Add Additional Fee or Discount
            </button>
          </div>
        );
      })}
    </div>
  );
};

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

export default ListCartFees;

const FeeLabel = ({
  cartItemType,
  count,
}: {
  cartItemType: string;
  count: number;
}) => {
  const getLabel = () => {
    switch (cartItemType) {
      case "license": {
        if (count > 0) {
          return `Year ${count}`;
        }

        return "License";
      }
      case "tag": {
        return "Tag";
      }
      case "fee": {
        return "Fee";
      }
      default: {
        return "";
      }
    }
  };

  return <h2 className="text-lg font-semibold text-gray-800">{getLabel()}</h2>;
};
