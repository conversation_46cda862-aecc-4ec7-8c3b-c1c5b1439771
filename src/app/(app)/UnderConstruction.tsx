import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Image from 'next/image';

const UnderConstruction: React.FC = () => {
  const siteTitle = 'ClerkXpress';
  const supportEmail = '<EMAIL>';
  const refreshInterval = 60;
  
  const [countdown, setCountdown] = useState(refreshInterval);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => (prev > 0 ? prev - 1 : refreshInterval));
    }, 1000);

    const refreshTimer = setTimeout(() => {
      window.location.reload();
    }, refreshInterval * 1000);

    return () => {
      clearInterval(timer);
      clearTimeout(refreshTimer);
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Head>
        <title>{siteTitle} - Under Maintenance</title>
        <meta name="description" content="Our site is currently undergoing maintenance. We'll be back shortly." />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="flex-grow flex flex-col items-center justify-center p-4 md:p-8 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div className="max-w-md w-full mx-auto bg-white rounded-xl shadow-lg overflow-hidden p-6 md:p-8 space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{siteTitle}</h1>
            <Image  
              src="/images/resident/saddog.png"
              alt="Under Construction"
              width={150}
              height={150}
              className="mx-auto mb-4"
            />
            <h2 className="text-xl font-semibold text-gray-800 mb-2">System Maintenance</h2>
            <p className="text-gray-600 mb-6">
              We&apos;re currently performing scheduled maintenance on our licensing and permitting platform. We&apos;ll be back online shortly.
            </p>
          </div>

          <div className="text-center text-gray-600 font-medium">
            <p>Refreshing in <span className="text-blue-600 font-bold">{countdown}</span> seconds...</p>
          </div>

          <div className="text-center mt-8">
            <p className="text-sm text-gray-500">
              If you need immediate assistance, please contact{' '}
              <a href={`mailto:${supportEmail}`} className="text-blue-600 hover:underline">
                {supportEmail}
              </a>
            </p>
          </div>
        </div>
      </main>

      <footer className="bg-white py-4 border-t border-gray-200">
        <div className="container mx-auto px-4 text-center text-sm text-gray-500">
          <p>&copy; {new Date().getFullYear()} {siteTitle}. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default UnderConstruction;
