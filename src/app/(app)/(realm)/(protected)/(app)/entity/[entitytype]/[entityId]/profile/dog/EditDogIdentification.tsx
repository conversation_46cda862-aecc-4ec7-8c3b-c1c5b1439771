import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dog } from "@/types/DogType";
import { useUpdateEntityDogProfile } from "@/hooks/api/useProfiles";
import {
  EditDialog,
  EditDialogFooter,
  createFormData,
} from "@/app/(app)/(realm)/(protected)/(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { useAtom } from "jotai";
import { useQueryClient } from "@tanstack/react-query";
import { toastAtom } from "@/components/ui/toast/toast";
import { Comment, TagNumber } from "@/components/dialog/EditFields";

type FormValues = {
  tagNumber: string;
  comment: string;
};

export const EditDogIdentification = ({
  dog,
  admin = false,
}: {
  dog: Dog;
  admin?: boolean;
}) => {
  const initialValues = {
    tagNumber: dog?.tagNumber || "",
    comment: "",
  };

  const updateEntityDogProfile = useUpdateEntityDogProfile();

  const {
    reset,
    register,
    control,
    handleSubmit,
    formState: { errors, isDirty },
    setValue
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });
  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    console.log(data);
    const formData = createFormData(data);

    updateEntityDogProfile.mutate(
      {
        entityId: dog.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Dog Updated",
            message: "Successfully Updated Dog Identification",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  return (
    <EditDialog
      title="Edit Dog Identification"
      isOpen={isOpen}
      setIsOpen={setIsOpen}
    >
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <TagNumber 
          errors={errors} 
          control={control}
        />
        <Comment register={register} />
        <EditDialogFooter handleCancel={handleCancel} disabled={!isDirty} />
      </form>
    </EditDialog>
  );
};

export default EditDogIdentification;
