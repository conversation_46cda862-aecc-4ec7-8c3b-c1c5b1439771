import { InputTextField } from "@/components/profile/ProfileComponents";
import { useState } from "react";

const ContactItem = ({
  label,
  initialValue,
  onSave,
  error,
}: {
  label: string;
  initialValue: string;
  onSave: (value: string) => void;
  error: any;
}) => {
  const [value, setValue] = useState(initialValue ?? "");
  const [edit, setEdit] = useState(false);

  const handleSave = (newVal: any) => {
    setValue(newVal);
    onSave(newVal);
  };

  return (
    <div className="mb-2 text-sm px-2 py-1 flex items-center gap-3">
      <label className="shrink-0">{label}:</label>

      <InputTextField
        value={value}
        onClick={(value) => {
          setEdit(false);
          handleSave(value);
        }}
        error={error}
        // className="w-full"
      />

      {/* <input
        type="text"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        className="ml-2 p-1 rounded w-full text-right focus:outline-1 border border-neutral-400"
      />
      {!edit && (
        <Button size="xs" variant="secondary" onClick={() => setEdit(!edit)}>
          Edit
        </Button>
      )}

      {edit && (
        <>
          <Button
            size="xs"
            variant="primary"
            onClick={() => {
              setEdit(false);
              handleSave();
            }}
          >
            Save
          </Button>
          <Button
            size="xs"
            variant="danger"
            onClick={() => {
              setEdit(false);
              setValue(initialValue);
            }}
          >
            Cancel
          </Button>
        </>
      )} */}
    </div>
  );
};

export default ContactItem;
