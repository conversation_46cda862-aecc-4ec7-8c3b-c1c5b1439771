import { entityDisplay } from "@/app/(app)/(realm)/(protected)/(app)/entity/[entitytype]/[entityId]/profile/entityHelper";
import { Badge } from "@/components/ui/badge";
import { BiEnvelope, BiPhone } from "react-icons/bi";
import ProfileAvatar from "@/components/profile/ProfileAvatar";
import { isSenior } from "@/components/license/licenseHelper";
import { useGetDocumentBlob } from "@/hooks/api/useServices";
import { License } from "@/types/LicenseType";
import LostDogAlert from "./header/LostDogAlert";
import { Dog } from "lucide-react";
import ProfileActions2 from "@/components/profile/ProfileActions2";
import { FiChevronDown } from "react-icons/fi";

const ProfileEntityHeader = ({
  entity,
  entityType,
  entityId,
}: {
  entity: any;
  entityType: string;
  entityId: string;
}) => {
  const ent = entityDisplay(entity[entityType], entityType);
  const currentEntity = entity[entityType];
  const isActive = ent?.active;
  const licenseCount = entity?.license?.filter(
    (l: License) => l?.status === "Active",
  )?.length;

  const outstandingAmount = currentEntity?.fees
    ? currentEntity?.fees?.totalOutstandingAmount
    : 0;

  const senior = currentEntity?.dateOfBirth
    ? isSenior(currentEntity?.dateOfBirth)
    : false;

  const avatarUrl: string =
    currentEntity?.documents?.find((doc: any) => doc.key === "avatar")
      ?.documentUuid ?? "";

  const enabled = avatarUrl !== "" || avatarUrl.length > 0;

  const {
    data: avatarBlob,
    isLoading,
    isError,
  } = useGetDocumentBlob((avatarUrl as string) ?? "", enabled);

  const toDollar = (amount: number) => {
    return `$${amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")}`;
  };

  return (
    <div
      className={`
       relative flex shrink-0 flex-col items-center bg-white md:px-6
    `}
    >
      <div className="mt-2 flex w-full shrink-0 items-center gap-6 md:mt-0">
        <div
          className="
          mx-auto mb-0 flex w-full flex-col items-center justify-between pt-0  text-center md:container
          md:mb-10 md:flex-row md:items-start md:pt-8 md:text-left
        "
        >
          <div
            className="
            flex min-h-[120px] w-full flex-col items-center justify-start gap-6 px-2 py-6 md:flex-row
            md:items-start md:gap-8 md:py-10
          "
          >
            {isLoading || isError ? (
              <ProfileAvatar
                entityId={entityId}
                entityType={entityType}
                isActive={isActive ?? false}
                avatarUrl={null}
                canEdit={true}
              />
            ) : (
              <ProfileAvatar
                entityId={entityId}
                entityType={entityType}
                isActive={isActive ?? false}
                avatarUrl={URL?.createObjectURL(avatarBlob)}
                canEdit={true}
                blob={avatarBlob}
              />
            )}

            <div className="flex h-full flex-col">
              {/* Header */}
              <div className="mt-6 flex flex-row items-center justify-center gap-2 md:justify-start lg:mt-0">
                <p className="text-xl font-bold tracking-tight md:text-3xl lg:mt-0">
                  {ent?.primaryDisplay || "No Name"}
                  {senior && (
                    <span className="text-lg text-neutral-600">, (Senior)</span>
                  )}
                </p>
                <div className="">
                  {!isActive && <Badge variant="destructive">Inactive</Badge>}
                </div>
              </div>

              {/* Address */}
              <div className="text-center text-base font-normal text-neutral-600 md:text-left">
                {ent?.secondaryDisplay ?? ""}
              </div>

              {/* Contacts */}
              <div className="text-center text-sm font-normal text-neutral-600 md:text-left">
                {ent?.contacts && (
                  <div className="flex flex-wrap items-center justify-center gap-0 md:justify-start md:gap-4">
                    <p className="flex items-center text-sm text-neutral-600">
                      <BiPhone className="mr-1 inline-block" />
                      {ent?.contacts?.phone}
                    </p>
                    <p className="flex items-center text-sm text-neutral-600">
                      <BiEnvelope className="mr-1 inline-block" />
                      {ent?.contacts?.email}
                    </p>
                  </div>
                )}
              </div>

              <LostDogAlert />

              {/* Events Dropdown */}
              <ProfileActions2
                entity={entity[entityType]}
                entityType={entityType}
                className="my- flex w-fit items-center justify-center gap-2 rounded bg-clerk-background px-4 py-1 text-sm text-white hover:bg-clerk-background/80 mt-2"
              >
                Actions <FiChevronDown />
              </ProfileActions2>

              {/* Info */}
              <div className="mt-2 flex justify-center gap-6 md:justify-start">
                <div className="hidden pr-2 md:block">
                  <div className="text-xs uppercase text-neutral-700">
                    No. of Active Licenses
                  </div>
                  <div className="text-lg font-medium">{licenseCount}</div>
                </div>

                {outstandingAmount > 0 && (
                  <div>
                    <div className="text-xs font-bold uppercase text-red-500">
                      Outstanding Balance
                    </div>
                    <div className="text-lg font-medium">
                      {outstandingAmount
                        ? toDollar(outstandingAmount)
                        : "$0.00"}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileEntityHeader;
