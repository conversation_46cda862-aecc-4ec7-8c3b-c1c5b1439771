"use client"
import React from "react";
import QuillToolbar from "../QuillToolbar";
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import { formats, modules } from "../quill/quillConfig";

if (typeof window !== 'undefined') {
  require('react-quill/dist/quill.snow.css');
}

const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
});

interface Props {
  body: string;
  setBody: (body: string) => void;
}


const EmailTemplateEditor= ({ body, setBody }:Props) => {

  return (
    <div className="h-full flex flex-col overflow-hidden">
      <QuillToolbar />
      <ReactQuill
        value={body}
        onChange={setBody}
        modules={modules}
        formats={formats}
        className="h-full overflow-auto"
      />
    </div>
  );
};

export default EmailTemplateEditor;
