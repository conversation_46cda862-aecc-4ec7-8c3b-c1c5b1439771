"use client";
import React from "react";
import FeeManager from "./FeeManager";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import FeeGroups from "./FeeGroups";
import { FeeProvider } from "./FeeContext";

export default function FeeBuilderPage() {
  const tab = useSearchParams().get("tab") || "feeSets";
  return (
    <Tabs
      defaultValue={tab}
      className="flex h-full w-full flex-col overflow-hidden"
    >
      <TabsList className="w-full border-b px-4">
        <TabButton label="Fee Sets" type="feeSets" />
        <TabButton label="Fee Codes" type="feeCodes" />
      </TabsList>
      <FeeProvider>
        <div className="flex overflow-hidden h-full w-full">
        <TabsContent value="feeSets" className="w-full">
          <FeeGroups />
        </TabsContent>
        <TabsContent value="feeCodes" className="w-full">
          <FeeManager />
        </TabsContent>
        </div>
      </FeeProvider>
    </Tabs>
  );
}

const TabButton = ({ label, type }: { label: string; type: string }) => {
  const { push } = useRouter();
  const pathname = usePathname();

  return (
    <TabsTrigger
      value={type}
      onClick={() => {
        push(pathname + "?tab=" + type);
      }}
    >
      {label}
    </TabsTrigger>
  );
};
