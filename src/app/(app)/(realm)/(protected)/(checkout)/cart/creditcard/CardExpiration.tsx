import React from 'react'


const CardExpiration = ({
  cardExpiration,
  setCardExpiration
}:{
  cardExpiration:string,
  setCardExpiration:React.Dispatch<React.SetStateAction<string>>
}) => {

const handleCardExpiration = (input: string) => {
  let value = input.replace(/\D/g, '');

  if (value.length >= 1) {
    const firstDigit = parseInt(value.slice(0, 1));

    if (firstDigit >= 2 && firstDigit <= 9) {
      value = '0' + value;
    }
  }

  if (value.length > 2) {
    const month = value.slice(0, 2);
    const year = value.slice(2, 4);

    if (parseInt(month) > 12) {
      value = `12/${year}`;
    } else {
      value = `${month}/${year}`;
    }
  }
    
  if (input.length > 0 && input[input.length-1] === ' ') {
    value = value.slice(0, -1);
  }
  setCardExpiration(value);
}



  return (
    <div className='flex flex-col w-1/2'>
      <label htmlFor='cardExpiration'>Card Expiration</label>
      <input
        type='tel'
        name='cardExpiration'
        id='cardExpiration'
        className={`bg-gray-100 rounded-md p-2`}
        maxLength={5}
        value={cardExpiration}
        onChange={(e) => { handleCardExpiration(e.target.value) }}
        placeholder='MM / YY'
      />
    </div>
  )
}

export default CardExpiration