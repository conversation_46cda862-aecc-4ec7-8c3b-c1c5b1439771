import React from "react";
import type { ActiveCartItems } from "@/types/CartType";

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

const ResidentCartFees = ({ cart }: { cart: ActiveCartItems }) => {
  return (
    <>
      <div className="mt-10 flex justify-between text-sm">
        <p>Subtotal</p>
        <p>{formatter.format(cart.subtotal)}</p>
      </div>

      {cart.summary &&
        cart.summary.map((fee, index) => {
          return (
            <div key={index} className="flex justify-between text-sm">
              <p>{fee.label}</p>
              <p>{formatter.format(fee.amount)}</p>
            </div>
          );
        })}
      {/* Total */}
      <div className="mt-2 flex justify-between py-2 text-lg font-bold">
        <p>Total</p>
        <p>{formatter.format(cart.total)}</p>
      </div>
    </>
  );
};

export default ResidentCartFees;
