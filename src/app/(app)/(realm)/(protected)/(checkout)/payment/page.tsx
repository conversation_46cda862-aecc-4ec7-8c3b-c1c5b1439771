import Payment from './Payment';
import BreadCrumbCustom from '@/components/ui/breadcrumbs/BreadCrumbCustom';

const PaymentPage = ({ 
  params
}:{
  params: {
    realm: string;
  };
}) => {
  const { realm } = params

  return (
    <div className='flex flex-col gap-6 w-full mx-auto p-6 bg-neutral-100 h-full'>
      <div className='container mx-auto'>
        <BreadCrumbCustom
          returnTo={`/cart`}
          label='Back to Cart'
        />
        <div className='flex gap-6'>
          <Payment />
        </div>
      </div>
    </div>
  )
}

export default PaymentPage