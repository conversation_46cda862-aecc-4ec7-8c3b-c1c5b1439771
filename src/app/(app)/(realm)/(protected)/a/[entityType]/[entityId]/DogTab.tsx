import { Dog } from "@/types/DogType";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { FiChevronDown, FiFile, FiPlus } from "react-icons/fi";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { LuActivitySquare, LuExternalLink } from "react-icons/lu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { License } from "@/types/LicenseType";
import AvatarImage, { getAvatarBlob } from "@/components/AvatarImage";
import { EntityFiles } from "@/components/files/EntityFiles";
import { Document } from "@/types/DocumentType";
import { EntityHistory } from "@/components/history/EntityHistory";
import Link from "next/link";
import ProfileActions2 from "@/components/profile/ProfileActions2";
import ProfileBuilderFactory from "@/components/builders/profileBuilder/ProfileBuilderFactory";
import { useEntity } from "@/hooks/providers/useEntity";

export const DogsContent = ({
  dogs,
  license,
}: {
  dogs: Dog[];
  license: License;
}) => {
  const pathname = usePathname();
  const router = useRouter();
  const firstDog = dogs?.length ? dogs[0]?.entityId : null;
  const searchParams = useSearchParams();
  const dogSelectionId = searchParams.get("dogId");
  const [selectedTab, setSelectedTab] = useState<string | null>(
    dogSelectionId || firstDog,
  );

  const licenseType = license?.licenseType?.code;

  const containerVariants = {
    hidden: { opacity: 0, y: -50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 100 },
    },
    exit: { opacity: 0, y: 50, transition: { duration: 0.2 } },
  };
  
  if (!dogs) return null;

  const selectedDog = dogs.find((dog: Dog) => dog.entityId === selectedTab);

  return (
    <>
      {(dogs.length > 1 || licenseType === "purebredDogLicense") && (
        <div className="flex w-[300px] flex-col gap-2 rounded bg-white p-2 shadow">
          {dogs.map((dog: Dog) => (
            <DogTab
              dog={dog}
              key={dog.entityId}
              selectedTab={selectedTab || ""}
              setSelectedTab={setSelectedTab}
            />
          ))}
          <button
            className={cn(
              "flex w-full gap-2 rounded border p-2 transition-all hover:bg-green-100",
            )}
            onClick={() => {
              router.push(
                `/license/dogLicenses/create/purebred?entityId=${license.entityId}&entityType=purebredDogLicense&action=addAdditionalDog&returnTo=${pathname}`,
              );
            }}
          >
            <div className="flex size-10 items-center justify-center rounded border">
              <FiPlus className="text-2xl" />
            </div>
            <div className={cn("flex flex-col text-left")}>
              <div className="font-medium">New Dog</div>
              <small>Add Dog to License</small>
            </div>
          </button>
        </div>
      )}
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedTab}
          // variants={containerVariants}
          // initial="hidden"
          // animate="visible"
          // exit="exit"
          className="w-full rounded bg-white p-3 shadow lg:p-6"
        >
          {selectedDog && <DogContent dog={selectedDog} />}
        </motion.div>
      </AnimatePresence>
    </>
  );
};

export const DogTab = ({
  dog,
  selectedTab,
  setSelectedTab,
}: {
  dog: Dog;
  selectedTab: string;
  setSelectedTab: (value: string) => void;
}) => {
  const active = selectedTab === dog?.entityId;
  return (
    <button
      className={cn(
        "flex w-full gap-2 rounded p-2 transition-all hover:bg-blue-100",
        active && "bg-blue-50",
      )}
      key={dog?.entityId}
      onClick={() => setSelectedTab(dog?.entityId)}
    >
      <AvatarImage
        entityType="dog"
        src={getAvatarBlob(dog?.documents) as string}
        alt="dog"
        width={active ? 40 : 40}
        height={active ? 40 : 40}
        className={cn(
          "h-fit rounded object-cover object-center shadow",
          active && "rounded-lg",
        )}
      />
      <div className={cn("flex flex-col text-left", active && "text-left")}>
        <div className={cn("", active && "font-medium")}>{dog.dogName}</div>
        <small className="text-neutral-600">{dog.dogBreed}</small>
      </div>
    </button>
  );
};

const DogContent = ({ dog }: { dog: Dog }) => {
  // const [isEventsOpen, setIsEventsOpen] = useState<boolean>(false);
  // const [isFilesOpen, setIsFilesOpen] = useState<boolean>(false);
  const { entityIsFetching, entityRefetch } = useEntity();
  const documents: { [key: string]: Document[] } = {
    "": dog?.documents ?? [],
  };
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-wrap gap-12">
        <div className="flex flex-row flex-wrap gap-4">
          <div
            style={{
              height: "100px",
              width: "100px",
            }}
            className="flex items-center justify-center overflow-hidden rounded"
          >
            <AvatarImage
              entityType="dog"
              src={getAvatarBlob(dog?.documents) as string}
              alt="dog"
              height={100}
              width={100}
              className="rounded object-cover object-center shadow"
              style={{ objectFit: "cover" }}
            />
          </div>
          <div className="flex flex-col">
            <div className="line-clamp-2 flex shrink-0 -translate-x-0.5 items-center gap-1 break-all py-1 text-4xl font-medium text-neutral-800">
              <Link
                href={`/profile/dog/${dog.entityId}?tab=profile`}
                className={cn(
                  " w-fit shrink-0 text-blue-600 hover:text-blue-500",
                )}
              >
                {dog.dogName}
              </Link>
              <Link
                href={`/profile/dog/${dog.entityId}?tab=profile`}
                target="_blank"
              >
                <LuExternalLink className="text-lg text-blue-600 hover:text-blue-500" />
              </Link>
            </div>
            <p className="mb-auto text-neutral-700 ">{dog?.dogBreed}</p>
            <ProfileActions2
              entity={dog}
              entityType="dog"
              className="mt-2 flex w-fit items-center justify-center gap-2 rounded bg-clerk-background px-4 py-1 text-sm text-white hover:bg-clerk-background/80"
            >
              Actions <FiChevronDown />
            </ProfileActions2>
          </div>
        </div>

        <div className="flex grow justify-end gap-6">
          <Sheet>
            <SheetTrigger
            // onClick={() => {
            //   setIsFilesOpen(true);
            // }}
            >
              <Button
                variant="ghost"
                className="flex h-fit flex-col items-center"
                // onClick={() => setIsFilesOpen(true)}
              >
                <FiFile className="text-3xl" />
                <div className="text-neutral-600">Files</div>
              </Button>
            </SheetTrigger>
            <DogFiles
              documents={documents}
              entityId={dog?.entityId}
              entityType="dog"
            />
          </Sheet>
          <Sheet>
            <SheetTrigger
            // onClick={() => {
            //   // setIsEventsOpen(true);
            // }}
            >
              <Button
                variant="ghost"
                className="flex h-fit flex-col items-center"
                // onClick={() => setIsEventsOpen(true)}
              >
                <LuActivitySquare className="text-3xl" />
                <div className="text-neutral-600">History</div>
              </Button>
            </SheetTrigger>
            <SheetContent className="overflow-y-auto">
              <SheetHeader className="mb-2">
                <SheetTitle>History</SheetTitle>
              </SheetHeader>

              <EntityHistory entityType={"dog"} entity={dog} sheet={true} />
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Profile */}
      <ProfileBuilderFactory entity={dog} profileType="dog" entityIsFetching={entityIsFetching} entityRefetch={entityRefetch} />
    </div>
  );
};

const DogFiles = ({
  documents,
  entityId,
  entityType,
}: {
  documents: {
    [key: string]: Document[];
  };
  entityId: string;
  entityType: string;
}) => {
  return (
    <SheetContent className="flex flex-col overflow-y-auto p-0">
      <EntityFiles
        documents={documents}
        entityType={entityType}
        entityId={entityId}
        sheet={true}
      />
    </SheetContent>
  );
};
