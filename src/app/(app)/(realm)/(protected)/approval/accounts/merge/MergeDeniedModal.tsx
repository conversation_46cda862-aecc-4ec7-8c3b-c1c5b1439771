import React, { useState } from "react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Controller, useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { useMergeApprovalContext } from "@/components/approval/hooks/useMergeApproval";
import { useDenyMergeRequest } from "@/hooks/api/useApprovals";
import { useQueryClient } from "@tanstack/react-query";
import { cn } from "@/lib/utils";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

export default function MergeDeniedModal({
  existingUserId
}:{
  existingUserId:string
}) {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      reason: "",
      comment: "",
    },
  });
  const { replace } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const residentId = searchParams.get("requestedUserId") as string
  const { mergeApprovalList, refetchMergeList } = useMergeApprovalContext();
  const queryClient = useQueryClient();

  const [_, setToast] = useAtom(toastAtom);

  const denyMutate = useDenyMergeRequest();
  const [isOpen, setIsOpen] = useState(false);

  const nextResident = () => {
    const currentResidentIndex = mergeApprovalList.findIndex(
      (item: any) => item.entityId === residentId,
    );

    const nextResidentIndex = currentResidentIndex + 1;

    if (nextResidentIndex >= mergeApprovalList.length) {
      return null;
    }

    return mergeApprovalList[nextResidentIndex].entityId;
  };

  const nextResidentId = nextResident();
  


  const onDeny = (body:{
    reason: string;
    comment: string;
  }) => {
    denyMutate.mutate(
      {
        ...body,
          existingUserIds: [
          existingUserId
        ],
        requestedUserId: residentId,
      },
      {
        onSuccess: () => {
          setToast({
            status: "success",
            label: "License Denied",
            message: `Merge request has been denied`,
          });
          reset();
          setIsOpen(false);
          refetchMergeList()
          queryClient.invalidateQueries()
          if(nextResidentId){
            replace(`${pathname}?requestedUserId=${nextResidentId}`)
          } else {
            replace(`${pathname}`)
          }
        },
        onError: (error: any) => {
          setToast({
            status: "error",
            label: "Error denying request",
            message: error.message,
          });
        }
      },
    );
  };

  const handleOpenChange = (newOpenState: boolean) => {
    setIsOpen(newOpenState);
    if (!newOpenState) {
      reset(); 
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger
        onClick={() => {
          setIsOpen(true);
        }}
      >
        <Button variant="destructive" size={"sm"}>Deny</Button>
      </DialogTrigger>
      <DialogContent className="max-w-lg mx-4 sm:mx-auto">
        <DialogHeader className="text-center space-y-3">
          <DialogTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            Deny Merge Request
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Please provide a reason and comments for denying this merge request.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onDeny)} className="space-y-6">
          {/* Merge Request Information Card */}
          <div className="bg-gradient-to-r from-red-50 to-red-100 rounded-xl p-6 border border-red-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              Request to be Denied
            </h3>
            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border border-gray-100">
                <p className="text-sm font-medium text-gray-600 mb-1">Action</p>
                <p className="text-base text-gray-900">
                  Account merge request will be denied and rejected
                </p>
              </div>
            </div>
          </div>

          {/* Denial Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason" className="text-sm font-semibold text-gray-900">
              Denial Reason *
            </Label>
            <Controller
              name="reason"
              control={control}
              rules={{ required: "Reason for denial is required." }}
              render={({
                field: { onChange, value },
                fieldState: { error },
              }) => (
                <div className="space-y-1">
                  <Select value={value} onValueChange={onChange}>
                    <SelectTrigger 
                      aria-invalid={error ? "true" : "false"}
                      className={cn(
                        "h-11 border-gray-300 focus:border-red-400 focus:ring-red-400",
                        error && "border-red-400 ring-1 ring-red-400"
                      )}
                    >
                      <SelectValue placeholder="Select denial reason" />
                    </SelectTrigger>
                    <SelectContent className="z-[9999]">
                      <SelectItem value="Information Mismatch">
                        Information Mismatch
                      </SelectItem>
                      <SelectItem value="Incomplete Information">
                        Incomplete Information
                      </SelectItem>
                      <SelectItem value="Missing Documentation">
                        Missing Documentation
                      </SelectItem>
                      <SelectItem value="Other Reason">
                        Other Reason
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {error && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {error.message}
                    </p>
                  )}
                </div>
              )}
            />
          </div>

          {/* Comments */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-sm font-semibold text-gray-900">
              Comments *
            </Label>
            <Controller
              name="comment"
              control={control}
              rules={{ required: "Denial comment is required." }}
              render={({ field, fieldState: { error } }) => (
                <div className="space-y-1">
                  <Textarea 
                    {...field} 
                    placeholder="Provide detailed comments about the denial..."
                    className={cn(
                      "min-h-[100px] border-gray-300 focus:border-red-400 focus:ring-red-400 resize-none",
                      error && "border-red-400 ring-1 ring-red-400"
                    )}
                  />
                  {error && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {error.message}
                    </p>
                  )}
                </div>
              )}
            />
          </div>

          {/* Warning Notice */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-red-800">Denial Confirmation</p>
                <p className="text-sm text-red-700 mt-1">
                  This action will permanently deny the merge request. The applicant will be notified.
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col-reverse sm:flex-row gap-3 pt-2">
            <Button
              type="button"
              variant="outline"
              className="flex-1 h-11 border-gray-300 hover:border-gray-400 hover:bg-gray-50 font-medium rounded-lg transition-all duration-200"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 h-11 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={denyMutate.isLoading}
            >
              {denyMutate.isLoading ? (
                <div className="flex items-center gap-2">
                  <LoadingSpinner className="w-4 h-4" />
                  Denying...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Deny Request
                </div>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}