import { SearchFormValues } from "@/components/searchBuilder/SearchLayout";

export const dogLicenseSearchForm: SearchFormValues[] = [
  {
    section: "Dog",
    value: "dog",
    columns: [
      {
        inputs: [
          {
            label: "License Number",
            fieldName: "licenseNumber",
            type: "text",
            defaultValue: "",
          },
          {
            label: "Name",
            fieldName: "dogName",
            type: "text",
            defaultValue: "",
          },
          {
            label: "Tag Number",
            fieldName: "dogTagNumber",
            type: "text",
            defaultValue: "",
          },
          {
            label: "Birth Year",
            fieldName: "dogBirthYear",
            type: "number",
            defaultValue: "",
          },
          {
            label: "Microchip Number",
            fieldName: "dogMicrochipNumber",
            type: "text",
            defaultValue: "",
          },
          {
            label: "Purebred",
            fieldName: "dogPurebred",
            type: "select",
            options: [
              { label: "Select", value: "" },
              { label: "Yes", value: "yes" },
              { label: "No", value: "no" },
            ],
          },
        ],
      },
      {
        inputs: [
          {
            label: "Breed",
            fieldName: "dogBreed",
            type: "text",
            defaultValue: "",
          },
          {
            label: "Primary Color",
            fieldName: "dogPrimaryColor",
            type: "select",
            defaultValue: "",
            options: [
              { label: "Select a color", value: "" },
              { label: "Black", value: "black" },
              { label: "White", value: "white" },
              { label: "Brown", value: "brown" },
              { label: "Tan", value: "tan" },
              { label: "Red", value: "red" },
              { label: "Cream", value: "cream" },
              { label: "Grey", value: "grey" },
              { label: "Blue", value: "blue" },
              { label: "Sable", value: "sable" },
              { label: "Brindle", value: "brindle" },
              { label: "Fawn", value: "fawn" },
              { label: "Merle", value: "merle" },
              { label: "Parti-color", value: "parti-color" },
              { label: "Spotted", value: "spotted" },
              { label: "Chocolate", value: "chocolate" },
              { label: "Liver", value: "liver" },
            ],
          },
          {
            label: "Secondary Color",
            fieldName: "secondaryColor",
            type: "select",
            defaultValue: "",
            options: [
              { label: "Select a color", value: "" },
              { label: "Black", value: "black" },
              { label: "White", value: "white" },
              { label: "Brown", value: "brown" },
              { label: "Tan", value: "tan" },
              { label: "Red", value: "red" },
              { label: "Cream", value: "cream" },
              { label: "Grey", value: "grey" },
              { label: "Blue", value: "blue" },
              { label: "Sable", value: "sable" },
              { label: "Brindle", value: "brindle" },
              { label: "Fawn", value: "fawn" },
              { label: "Merle", value: "merle" },
              { label: "Parti-color", value: "parti-color" },
              { label: "Spotted", value: "spotted" },
              { label: "Chocolate", value: "chocolate" },
              { label: "Liver", value: "liver" },
            ],
          },
          {
            label: "Sex",
            fieldName: "dogSex",
            type: "select",
            defaultValue: "",
            options: [
              { label: "Select a Sex", value: "" },
              { label: "Male", value: "male" },
              { label: "Female", value: "female" },
            ],
          },
          {
            label: "Spayed or Neutered",
            fieldName: "dogSpayedNeutered",
            type: "select",
            defaultValue: "",
            options: [
              { label: "Select", value: "" },
              { label: "Yes", value: "yes" },
              { label: "No", value: "no" },
            ],
          },
        ],
      },
    ],
  },
];
