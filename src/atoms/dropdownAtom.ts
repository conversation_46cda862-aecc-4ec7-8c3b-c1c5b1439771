import { atom } from "jotai";

type MenuItem = {
  label: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  shortcut?: string;
};

type MenuGroupProps = {
  title?: string;
  items: MenuItem[];
};

type MenuProps = {
  title: string;
  contents: {
    title?: string;
    items: (MenuGroupProps | MenuItem)[];
  };
};

export const menuAtom = atom<MenuProps | null>(null);
