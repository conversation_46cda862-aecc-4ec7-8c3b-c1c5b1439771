import { atom } from "jotai";

export const resultsAtom = atom<any>([]);
export const currentStepAtom = atom<number>(0);
export const currentStepIndexAtom = atom<any>(1);
export const returnToAtom = atom<string | undefined>("");
export const formDataAtom = atom<any>([]);
export const stepsAtom = atom<any>([]);
export const entityIdAtom = atom<any>(null);
export const formInputsAtom = atom<any>([]);
export const extractedFileValuesAtom = atom<any>(null);
export const confirmDetailsAtom = atom<any>(null);