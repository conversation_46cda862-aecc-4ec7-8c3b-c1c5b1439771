import { Document } from "./DocumentType";

export interface DogEvent {
  uuid: string;
  code: string;
  name: string;
  description: string;
  createdBy: string;
  createdDate: string;
  comment: string | null;
}

export type FormValues = {
  [key: string]: any;
};

export interface Dog {
  // General Information
  entityId: string;
  entityType: string;
  name: string; // EntityType of the dog
  participantType: "Normal Dog";
  status: "Active" | "Deceased" | "Expired" | "Lost" | "Found" | "Transferred";
  active: boolean;
  events: DogEvent[];
  avatarUUID?: string;

  // Basic Information
  dogName: string;
  dogBirthDate: string;
  dogSpayedOrNeutered: "yes" | "no";
  dogSex: "Male" | "Female" | "male" | "female";

  // Physical Characteristics
  dogBreed: string;
  dogPrimaryColor: string;
  dogSecondaryColor?: string;
  breed?: string;

  // Extra Information
  dogMarkings?: string;
  dogBio?: string;

  // Identification
  microchipNumber?: string;
  tagNumber: string;

  // Behavioral
  isDangerous: boolean;
  catFriendly: boolean;
  dogFriendly: boolean;
  childFriendly: boolean;

  // Exemptions
  isAlteredExempt: boolean;
  licenseExempt: boolean;
  serviceDogType?: string | null | undefined;

  // Insurance
  insuranceStartDate?: string;
  insuranceExpirationDate?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  insuranceCompany?: string;
  insuranceEndDate?: string;

  // Vaccine Info
  vaccinePeriod?: string;
  veterinaryName?: string;
  veterinarianName?: string;
  vaccineDatesExempt: boolean;
  rabiesTagNumber?: string;
  vaccineName?: string;
  vaccineProducer?: string;
  vaccineBrand?: string;
  vaccineAdministeredDate?: string;
  vaccineDueDate?: string;
  vaccineLotExpirationDate?: string | null | undefined;
  vaccineLotNumber?: string | null | undefined;
  
  // Documents
  documents?: Document[];
  dogSpayedOrNeuteredExemptionDocument: any;
  dogRabiesVaccinationExemptionDocument: any;
  dogRabiesVaccinationDocument: any;
  dogSpayedOrNeuteredDocument: any;
  dogServiceAnimalExemptionDocument:any;

  // Approvals
  basicInfoApproved: boolean;
  behaviorApproved: boolean;
  insuranceApproved: boolean;
  vaccineApproved: boolean;
  physicalCharacteristicsApproved: boolean;

  fees?: any;

  // Impoundment
  dogImpoundmentDate?: string;

  // Rejected Fields
  rejectedFields?: string[];
}
