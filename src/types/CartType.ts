export type ActiveCartFee = {
  feeCode: string;
  label: string;
  feeAmount: number;
  licenseActivityId: string;
  feeId: string;
  licenseActivityFeeId: number;
  entityFeeId: string;
};

export type ActiveCartItem = {
  cartItemId: number;
  itemId: string;
  itemType: string;
  primaryDisplay: string;
  secondaryDisplay: string;
  fees: ActiveCartFee[];
  licenseActivities?: {
    validToDate: number;
    validFromDate: number;
    licenseActivityId: string;
  }[];
  subtotal: number;
  discountedItems: ActiveCartFee[];
  discount: number;
  total: number;
  licenseActivityFeeIds: string[];
};

export type ActiveCartItemsSummary = {
  label: string;
  amount: number;
  items: ActiveCartFee[];
};

export type ActiveCartItems = {
  cartId: string;
  userId: string;
  cartStatus: string;
  createdDate: string;
  items: ActiveCartItem[];
  total: number;
  subtotal: number;
  summary: ActiveCartItemsSummary[];
  hasAdditionalFee: boolean;
};


export type AllCartsItem = {
  cartId: string;
  userId: string;
  status: string;
  createdDate: string;
  updatedDate: string;
  itemNames: string[];
};

export type AllCarts = {
  items: AllCartsItem[];
  totalCount: number;
  pageSize: number;
  pageIndex: number;
};
