package com.scube.ai.seed;

import com.scube.ai.entity.Prompt;
import com.scube.ai.repository.PromptRepository;
import com.scube.multi.tenant.tenancy.tenant_runner.LoopPerTenant;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PromptSeeder implements CommandLineRunner {
    private final PromptRepository promptRepository;

    @Override
    @LoopPerTenant
    public void run(String[] args) throws Exception {
        loadPrompts();
    }

    private void loadPrompts() {
        if (promptRepository.count() == 0) {
            Prompt idPrompt = Prompt.builder()
                    .name("ID Front")
                    .description("ID Front")
                    .promptKey("idFront")
                    .promptText("""
                                   You are a json exporter. Fill in the (fields) from a string of text. If you can't find something leave it blank. Return all answers back in JSON. \s

                                   Example:   
                                                                                           
                                   fields = {"firstName": "", "lastName": "", "address": "", "address2": "", "dob": "", "state": "", "city": "", "zip": ""}
                                                                                           
                                   Input: NEW YORK STATE™ Tebzifetri DRIV NSE yg e a2 = = T st S oS 0123 456 789 D 00 • MOTORIST N N 5 MICHELLE, MARIE V A B 2345 ANYWHERE STREET A N ALBANY, NY 12222-1242 2 1\\n    b i ope 10/31/1990 v V 100 03/07/2022 oven . N Wi |, Bons10/31/2029 - & HEH Moty « Nove 7\\n    AEVE A r e R NONE aoiVA (6%f %&-F Height 5'-08 Eves BRO. pi * el\\n\\n\s
                                                                                           
                                   Output: {"firstName": "Michelle", "middleName": "Marie", "lastName": "Motorist", "address": "2345 Anywhere St", "address2": "", "": "Albany", "state": "NY", "zip": "12203"}
                            """)
                    .build();
            promptRepository.save(idPrompt);

            Prompt vaccinePrompt = Prompt.builder()
                    .name("Vaccine prompt")
                    .description("Vaccine prompt")
                    .promptKey("vaccineRecord")
                    .promptText("""
                                    Prompt: You are a json exporter. Fill in the (fields) from a string of text. If you can't find something leave it blank. Return all answers back in JSON.

                                    Example:

                                    fields = {"tagNumber": "", "dogName": "", "dogBreed": "", "dogBirthDate": "", "dogSex":"", "dogPrimaryColor": "", "dogSecondaryColor": "", "microchipNumber": "", "dogMarkings": "", "veterinaryName": "", "veterinarianName": "", "rabiesTagNumber": "", "vaccineName": "", "vacineProducer": "", "vaccineAdministeredDate": "", "vaccineDueDate": "", "vaccineLotNumber": "", "vaccineLotExpirationDate": ""}

                                    Input: ‘carmen Ayala\n612 Unlonst\n\nApts\n\nSchenectady, NY 12305\nHome Phone: (*************\nCellPhone: (*************,\n\nBanfield Rabies Certificate\nPETHOSPTTAL\nCertificate Date\nPat Pt Hepa\n06 Battowa, Saturday, February 4, 2023 12:19 PM.\nSehenecay HY 23062045\n(sin)areee\n\n \n\n \n\n  \n\nPet Information\n\nName:\n\nSpecies:\n\nBreed:\n\nSex: Female (Spayed)\nColor: Bywht\n\nAge: 2y/4m\n\nWeight: 43.001bs\n\n \n\nManufacturer: Home Again/Merck\nManufacturer:\n\n \n\n \n\nLot/Seral Expires: 4/18/2023,\n\nregulations on this date\n\n \n\nhereby certify that thls pet has been vaccinated In accordance with al state and Federal laws and\n\nRoute: Subcutaneous ‘She: Right Rear\n\n \n\n \n\nVeterinarian: he *, A\n\n \n\n \n\nLicensed Veterinarian: Dr. Christina Marie\nPerogine\nDVM License Number: 011470\n\n \n\n \n\n‘Any rabies certificate for this pet printed prior to the date ofthis certificate is mull and void\n\n \n\nRepo: Rabeserens\n‘Time rind 1239 9M\n\nHaspal 392 Page ott\n\nDane Ptd: 2/4/2023   

                                    Output: {"tagNumber": "None", "dogName": "Lilah Ayala", "dogBreed": "Pit Bull", "dogBirthDate": "", "dogSex":"Female", "dogPrimaryColor": "gr", "dogSecondaryColor": "wht", "microchipNumber": "985112011977890", "dogMarkings": "", "veterinaryName": "Banfield PET HOSPITAL", "veterinarianName": "Dr. Christina Marie Perogine", "rabiesTagNumber": "", "vaccineName": "Rabies", "vacineProducer": "Zoetis", "vaccineAdministeredDate": "", "vaccineDueDate": "2/7/2022", "vaccineLotNumber": "535242", "vaccineLotExpirationDate": "4/18/2022"}
                            """)
                    .build();
            promptRepository.save(vaccinePrompt);
        }
    }
}