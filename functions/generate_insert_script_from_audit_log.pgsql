CREATE OR REPLACE FUNCTION generate_insert_script_from_audit_log(
    _schema_name text,
    _audit_table_name text,
    _condition text,
    _exclude_columns text[] DEFAULT '{}'
)
RETURNS text
AS $$
DECLARE
    _insert_script text := '';
    _row jsonb;
    _column_name text;
    _column_value text;
    _target_table_name text := regexp_replace(_audit_table_name, '^audit_log_', '');
    _columns text[];
BEGIN
    -- Get target table columns
    SELECT array_agg(column_name ORDER BY ordinal_position)
    INTO _columns
    FROM information_schema.columns
    WHERE table_name = _target_table_name
      AND table_schema = _schema_name;

    -- Force ORDER BY revision_id DESC LIMIT 1
    FOR _row IN EXECUTE format(
        'SELECT row_to_json(rr)
         FROM %I.%I rr
         WHERE %s
         ORDER BY revision_id DESC',
        _schema_name, _audit_table_name, _condition
    )
    LOOP
        _insert_script := _insert_script || format(
            'INSERT INTO %I.%I (',
            _schema_name, _target_table_name
        );

        -- Column list
        FOREACH _column_name IN ARRAY _columns LOOP
            IF _column_name = ANY(_exclude_columns) THEN CONTINUE; END IF;

            IF _column_name IN ('created_by', 'created_date') THEN
                _insert_script := _insert_script || _column_name || ', ';
            ELSE
                _insert_script := _insert_script || quote_ident(_column_name) || ', ';
            END IF;
        END LOOP;

        _insert_script := left(_insert_script, length(_insert_script) - 2) || ') VALUES (';

        -- Values list
        FOREACH _column_name IN ARRAY _columns LOOP
            IF _column_name = ANY(_exclude_columns) THEN CONTINUE; END IF;

            IF _column_name = 'created_by' THEN
                _column_value := quote_literal(_row ->> 'last_modified_by');
            ELSIF _column_name = 'created_date' THEN
                _column_value := quote_literal(_row ->> 'last_modified_date');
            ELSE
                _column_value := quote_literal(_row ->> _column_name);
            END IF;

            IF _column_value IS NULL THEN
                _insert_script := _insert_script || 'NULL, ';
            ELSE
                _insert_script := _insert_script || _column_value || ', ';
            END IF;
        END LOOP;

        _insert_script := left(_insert_script, length(_insert_script) - 2) || ');' || E'\n';
    END LOOP;

    RETURN trim(_insert_script);
END;
$$ LANGUAGE plpgsql;
