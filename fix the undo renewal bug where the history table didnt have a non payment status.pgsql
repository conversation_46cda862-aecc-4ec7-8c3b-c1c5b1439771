with cte as (
	SELECT 
		ROW_NUMBER() OVER (PARTITION BY a.license_number ORDER BY revision_id) as row_num,
		*
	FROM audit_log_license a
	WHERE NOT EXISTS (
	    SELECT 1
	    FROM audit_log_license i
		inner join license_status ls
		on ls.license_status_id = i.license_status_id
	    WHERE i.license_id = a.license_id
	      AND ls.code in ('APPROVED', 'ACTIVE', 'CANCELED', 'CLOSED','EXPIRED', 'DRAFT','PENDING')
	)
),
to_update as (
	select 
		case 
			when a.valid_to_date > now() then 9
			else 10
		END AS new_license_status_id,	
		a.license_id,
	    a.revision_id
	from audit_log_license a
	inner join cte c
	on a.license_id  = c.license_id
	and a.revision_id = c.revision_id 
	where c.row_num = 1
)
UPDATE audit_log_license a
SET license_status_id = t.new_license_status_id
FROM to_update t
WHERE a.license_id = t.license_id
  AND a.revision_id = t.revision_id;
