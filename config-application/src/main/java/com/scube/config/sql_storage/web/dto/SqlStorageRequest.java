package com.scube.config.sql_storage.web.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SqlStorageRequest {
    @Size(max = 255)
    @NotBlank
    private String name;

    private String description;

    @NotBlank
    private String sql;

    public SqlStorageRequest(String name, String sql) {
        this.name = name;
        this.sql = sql;
    }
}