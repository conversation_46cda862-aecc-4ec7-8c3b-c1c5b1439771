package com.scube.config.json_storage.rabbit;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.config.json_storage.JsonStorageService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
class CreateUpdateJsonStorageCommandHandler extends FanoutListener<CreateUpdateJsonStorageCommand> {
    private final JsonStorageService jsonStorageService;

    @Override
    public void consume(CreateUpdateJsonStorageCommand event) {
        jsonStorageService.createUpdate(event.properties(), event.jsonStorage());
    }
}

record CreateUpdateJsonStorageCommand(Map<String, Object> properties,
                                      JsonNode jsonStorage) implements IRabbitFanoutSubscriber {
}