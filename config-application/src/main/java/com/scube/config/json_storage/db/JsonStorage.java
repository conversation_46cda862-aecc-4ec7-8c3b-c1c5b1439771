package com.scube.config.json_storage.db;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.audit.auditable.entity.AuditableBaseWithProperties;
import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.envers.Audited;
import org.springframework.util.ObjectUtils;

import java.util.Map;

@Entity
@Table(
        name = JsonStorage.TABLE_NAME,
        uniqueConstraints = @UniqueConstraint(columnNames = {AuditableBaseWithProperties.A_PROPERTIES})
)
@Getter
@Setter
@Audited
@NoArgsConstructor
@Accessors(chain = true)
@EntityListeners(JsonStoragePublishChangedEvent.class)
public class JsonStorage extends AuditableEntity {
    public static final String TABLE_NAME = "json_storage";

    @Convert(converter = JsonStorageToJsonConverter.class)
    @Column(name = "json_data", columnDefinition = "jsonb", nullable = false)
    @ColumnTransformer(write = "?::jsonb")
    private JsonNode jsonData;

    public JsonStorage(Map<String, Object> properties, JsonNode jsonData) {
        if (ObjectUtils.isEmpty(properties) || ObjectUtils.isEmpty(jsonData)) {
            throw new IllegalArgumentException("Properties and jsonData must not be empty");
        }
        this.setProperties(properties);
        this.jsonData = jsonData;
    }
}