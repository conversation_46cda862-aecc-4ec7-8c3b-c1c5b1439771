package com.scube.documenttemplates.permissions;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.documenttemplates.SharedTestConfig;
import com.scube.documenttemplates.permission.Permissions;
import org.junit.jupiter.api.Test;

import static com.scube.documenttemplates.permissions.MockMvcHelper.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

class DocumentTemplateControllerTest extends SharedTestConfig {

    @Test
    @WithJwt(json = START_JSON + Permissions.DocumentTemplate.DOWNLOAD + END_JSON)
    void testDownload_Success() throws Exception {
        var result = performGet(mockMvc, "/templates/test/download");
        assertNotEquals(403, result.getResponse().getStatus());
    }

    @Test
    void testDownload_Failure() throws Exception {
        var result = performGet(mockMvc, "/templates/test/download");
        assertEquals(403, result.getResponse().getStatus());
    }
}