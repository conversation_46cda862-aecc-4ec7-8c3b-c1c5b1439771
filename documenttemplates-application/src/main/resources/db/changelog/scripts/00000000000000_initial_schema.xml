<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="james (generated)" id="1690899336154-1">
        <createTable tableName="template">
            <column autoIncrement="true" name="template_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="template_pkey"/>
            </column>
            <column name="category" type="VARCHAR(255)"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="document_uuid" type="UUID"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="sub_category" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
