package com.scube.imageprocessing.service.ocr.textract.util;

import com.scube.imageprocessing.service.ocr.textract.dto.IdResponse;
import software.amazon.awssdk.services.textract.model.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.text.WordUtils.capitalizeFully;

public class TextractUtil {
    public static HashMap<String, String> extractKeyValuePairs(List<Block> blocks) {
        HashMap<String, String> keyValuePairs = new HashMap<>();

        // Filter blocks to get key and value blocks
        List<Block> keyBlocks = blocks.stream()
                .filter(block -> BlockType.KEY_VALUE_SET.toString().equals(block.blockType())
                        && block.entityTypes().contains(EntityType.KEY))
                .collect(Collectors.toList());

        List<Block> valueBlocks = blocks.stream()
                .filter(block -> BlockType.KEY_VALUE_SET.toString().equals(block.blockType())
                        && block.entityTypes().contains(EntityType.VALUE))
                .collect(Collectors.toList());

        // Map of block Ids to block objects for quick access
        Map<String, Block> blockMap = blocks.stream()
                .collect(Collectors.toMap(Block::id, block -> block));

        for (Block keyBlock : keyBlocks) {
            String keyText = extractText(keyBlock, blockMap);
            String valueText = extractValueText(keyBlock, blockMap);
            keyValuePairs.put(keyText, valueText);
        }

        return keyValuePairs;
    }

    private static String extractText(Block block, Map<String, Block> blockMap) {
        if (block == null || block.relationships() == null) {
            return "";
        }

        return block.relationships().stream()
                .filter(rel -> "CHILD".equals(rel.typeAsString()))
                .flatMap(rel -> rel.ids().stream())
                .map(blockMap::get)
                .filter(childBlock -> BlockType.LAYOUT_TEXT.toString().equals(childBlock.blockType()))
                .map(Block::text)
                .collect(Collectors.joining(" "));
    }

    private static String extractValueText(Block keyBlock, Map<String, Block> blockMap) {
        Optional<Relationship> valueRelationship = keyBlock.relationships().stream()
                .filter(rel -> "VALUE".equals(rel.typeAsString()))
                .findFirst();

        if (!valueRelationship.isPresent()) {
            return "";
        }

        String valueId = valueRelationship.get().ids().get(0);
        Block valueBlock = blockMap.get(valueId);

        return extractText(valueBlock, blockMap);
    }

    public static Map<String, String> extractKeyValuePairs(AnalyzeIdResponse response) {
        Map<String, String> keyValuePairs = new HashMap<>();

        // Get the list of Identity Documents from the response
        List<IdentityDocumentField> documentFields = response.identityDocuments().get(0).identityDocumentFields();

        for (IdentityDocumentField field : documentFields) {
            String key = field.type().text();
            String value = field.valueDetection().text();
            keyValuePairs.put(key, value);
        }

        return keyValuePairs;
    }

    public static IdResponse of(AnalyzeIdResponse response) {
        Map<String, String> keyValuePairs = extractKeyValuePairs(response);

        IdResponse idResponse = new IdResponse();

        idResponse.setFirstName(capitalizeFully(keyValuePairs.get("FIRST_NAME")));
        idResponse.setMiddleName(capitalizeFully(keyValuePairs.get("MIDDLE_NAME")));
        idResponse.setLastName(capitalizeFully(keyValuePairs.get("LAST_NAME")));
        idResponse.setSuffix(capitalizeFully(keyValuePairs.get("SUFFIX")));
        idResponse.setDateOfBirth(keyValuePairs.get("DATE_OF_BIRTH"));
        idResponse.setAddress(capitalizeFully(keyValuePairs.get("ADDRESS")));
        idResponse.setCity(capitalizeFully(keyValuePairs.get("CITY_IN_ADDRESS")));
        idResponse.setState(keyValuePairs.get("STATE_IN_ADDRESS"));
        idResponse.setZip(keyValuePairs.get("ZIP_CODE_IN_ADDRESS"));

        String idType = keyValuePairs.get("ID_TYPE");
        if (idType.equalsIgnoreCase("DRIVER LICENSE FRONT")) {
            idResponse.setIdentificationType("idCardFront");
        } else if (idType.equalsIgnoreCase("PASSPORT")) {
            idResponse.setIdentificationType("passport");
        } else {
            idResponse.setIdentificationType("");
        }

        return idResponse;
    }
}
