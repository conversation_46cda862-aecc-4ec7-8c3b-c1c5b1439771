package com.scube.imageprocessing.service.ocr.textract;

import com.scube.imageprocessing.service.PdfService;
import com.scube.imageprocessing.service.ocr.textract.dto.IdResponse;
import com.scube.imageprocessing.service.ocr.textract.s3.S3FileStorageService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

import static com.scube.imageprocessing.config.AwsLocalConfig.staticS3Client;
import static com.scube.imageprocessing.config.AwsLocalConfig.staticTextractClient;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Disabled("This must be run locally.")
public class TextractAnalyzeIdTest {

    static byte[] getResource(String fileName) throws IOException {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        URL url = classLoader.getResource("images/" + fileName);
        String decodedPath = URLDecoder.decode(url.getPath(), StandardCharsets.UTF_8.name());
        File image =  new File(decodedPath);
        return Files.readAllBytes(image.toPath());
    }

    static S3FileStorageService s3FileStorageService = new S3FileStorageService(staticS3Client());
    static PdfService pdfService = new PdfService();
    static TextractOCRService textractOCRService = new TextractOCRService(staticTextractClient(), s3FileStorageService, pdfService);

    @Test
    void analyzeIdTestJpg() throws IOException {
        byte[] image = getResource("ny_drivers_license.jpg");

        IdResponse idResponse = textractOCRService.analyzeId(image);

        verifyResponse(idResponse);
    }

    @Test
    void analyzeIdTestPdf() throws IOException {
        byte[] image = getResource("ny_drivers_license.pdf");

        IdResponse idResponse = textractOCRService.analyzeId(image);

        verifyResponse(idResponse);
    }

    private static void verifyResponse(IdResponse idResponse) {
        assertEquals(idResponse.getFirstName(), "Michael");
        assertEquals(idResponse.getMiddleName(), "M");
        assertEquals(idResponse.getLastName(), "Motorist");
        assertEquals(idResponse.getDateOfBirth(), "08/31/1978");
        assertEquals(idResponse.getAddress(), "2345 Anywhere Street");
        assertEquals(idResponse.getCity(), "Your City");
        assertEquals(idResponse.getState(), "NY");
        assertEquals(idResponse.getZip(), "12345");
        assertEquals(idResponse.getIdentificationType(), "idCardFront");
    }
}
